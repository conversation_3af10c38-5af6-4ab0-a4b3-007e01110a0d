Search.setIndex({"docnames": ["about_gym", "api/index", "api/python/const_py", "api/python/enum_py", "api/python/gym_py", "api/python/index", "api/python/struct_py", "examples/assets", "examples/index", "examples/rl", "examples/simple", "faqs", "index", "install", "programming/assets", "programming/forcesensors", "programming/graphics", "programming/index", "programming/math", "programming/physics", "programming/simsetup", "programming/tensors", "programming/terrain", "programming/tuning", "release-notes"], "filenames": ["about_gym.rst", "api/index.rst", "api/python/const_py.rst", "api/python/enum_py.rst", "api/python/gym_py.rst", "api/python/index.rst", "api/python/struct_py.rst", "examples/assets.rst", "examples/index.rst", "examples/rl.rst", "examples/simple.rst", "faqs.rst", "index.rst", "install.rst", "programming/assets.rst", "programming/forcesensors.rst", "programming/graphics.rst", "programming/index.rst", "programming/math.rst", "programming/physics.rst", "programming/simsetup.rst", "programming/tensors.rst", "programming/terrain.rst", "programming/tuning.rst", "release-notes.rst"], "titles": ["About Isaac Gym", "API Reference", "Python Constants and Flags", "Python Enums", "Python Gym API", "Python API", "Python Structures", "Bundled Assets", "Examples", "Reinforcement Learning Examples", "Programming Examples", "Frequently Asked Questions", "Welcome to Isaac Gym\u2019s documentation!", "Installation", "Assets", "Force Sensors", "Graphics and Camera Sensors", "Programming", "Math Utilities", "Physics Simulation", "Simulation Setup", "Tensor API", "Terrains", "Simulation Tuning", "Release Notes"], "terms": {"nvidia": [0, 9, 13, 22, 23, 24], "s": [0, 4, 6, 10, 11, 13, 15, 16, 19, 20, 21, 23, 24], "prototyp": 0, "physic": [0, 6, 11, 12, 14, 16, 17, 20, 23, 24], "simul": [0, 3, 4, 6, 10, 12, 13, 14, 15, 16, 17, 22, 24], "environ": [0, 4, 6, 12, 14, 16, 17, 19, 21, 22, 23], "reinforc": [0, 8, 11, 12, 20, 22, 24], "learn": [0, 8, 11, 12, 16, 20, 22, 24], "research": [0, 20], "allow": [0, 4, 13, 14, 16, 19, 20, 21, 23, 24], "develop": [0, 13, 20, 23], "experi": [0, 21, 24], "end": [0, 4, 6, 10, 11, 16, 20, 24], "gpu": [0, 4, 6, 10, 11, 13, 19, 20, 21, 23, 24], "acceler": [0, 11, 23], "rl": 0, "base": [0, 4, 6, 11, 20, 21, 23, 24], "system": [0, 3, 6, 13, 16, 20, 23], "unlik": [0, 19, 23], "other": [0, 3, 4, 6, 10, 11, 13, 14, 16, 20, 21, 23, 24], "similar": [0, 23], "style": 0, "can": [0, 4, 6, 10, 11, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24], "run": [0, 6, 10, 11, 13, 14, 16, 17, 19, 21, 23, 24], "store": [0, 14, 16, 19, 21, 23], "result": [0, 11, 14, 16, 19, 20, 21, 24], "tensor": [0, 3, 4, 6, 10, 12, 14, 17, 20, 24], "rather": [0, 19, 20], "than": [0, 6, 10, 11, 13, 14, 16, 19, 20, 21, 23, 24], "copi": [0, 6, 11, 16, 19, 20, 21, 24], "them": [0, 10, 13, 16, 19, 20, 21, 23, 24], "back": [0, 6, 16, 21, 23], "cpu": [0, 4, 6, 10, 16, 19, 20, 21, 23, 24], "memori": [0, 4, 11, 16, 21], "A": [0, 3, 4, 6, 10, 14, 15, 16, 19, 20, 21, 23, 24], "api": [0, 11, 12, 17, 20, 22], "provid": [0, 4, 6, 13, 16, 19, 20, 21, 22], "access": [0, 4, 10, 11, 15, 19, 20, 21], "observ": [0, 16, 24], "reward": 0, "calcul": [0, 3, 6, 11, 16, 24], "also": [0, 10, 11, 13, 14, 15, 16, 19, 20, 21, 22, 23], "take": [0, 11, 14, 16, 19, 20, 21, 24], "place": [0, 4, 16, 19, 20, 21, 24], "thi": [0, 4, 6, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "combin": [0, 6, 21], "permit": [0, 6, 23], "thousand": [0, 20], "simultan": 0, "singl": [0, 4, 6, 11, 14, 16, 19, 20, 21, 23, 24], "previous": [0, 15, 21, 24], "mai": [0, 4, 6, 13, 14, 16, 19, 20, 21, 23, 24], "have": [0, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "requir": [0, 11, 13, 14, 16, 19, 20, 21, 23], "an": [0, 3, 4, 6, 10, 12, 14, 16, 19, 20, 21, 22, 23, 24], "entir": [0, 11, 14, 16, 19, 20, 21], "data": [0, 3, 4, 6, 10, 11, 15, 16, 19, 20, 21, 24], "center": [0, 3, 4, 6, 14, 15, 19, 21, 24], "workstat": [0, 20], "includ": [0, 4, 10, 13, 14, 15, 16, 19, 20, 23, 24], "basic": [0, 20, 23], "ppo": 0, "implement": [0, 20, 24], "straightforward": 0, "task": [0, 13, 19, 20, 21, 23, 24], "us": [0, 3, 4, 6, 7, 10, 11, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24], "user": [0, 4, 11, 15, 16, 19, 20, 21, 24], "substitut": 0, "altern": [0, 19, 21], "algorithm": [0, 11, 21], "desir": [0, 16, 19, 20], "core": [0, 20, 21, 23], "featur": [0, 6, 16, 20, 21, 23], "support": [0, 3, 4, 10, 13, 14, 15, 16, 19, 20, 21, 23, 24], "import": [0, 6, 13, 14, 16, 19, 20, 21, 23, 24], "urdf": [0, 4, 10, 14, 19, 20, 24], "mjcf": [0, 4, 10, 14, 19, 20, 24], "file": [0, 4, 6, 10, 11, 13, 14, 16, 20, 23, 24], "evalu": 0, "state": [0, 2, 4, 6, 10, 11, 17, 20, 23, 24], "appli": [0, 4, 6, 11, 15, 16, 19, 20, 21, 23, 24], "action": [0, 4, 6, 10, 11, 20, 21, 24], "varieti": [0, 19, 20], "sensor": [0, 3, 4, 6, 10, 11, 12, 17, 20, 22, 24], "posit": [0, 3, 4, 6, 11, 16, 19, 20, 21, 23, 24], "veloc": [0, 3, 4, 6, 10, 12, 19, 21, 23], "forc": [0, 3, 4, 6, 11, 12, 13, 14, 16, 17, 19, 23, 24], "torqu": [0, 3, 4, 10, 11, 15, 19, 21, 24], "etc": [0, 10, 12, 19, 24], "runtim": [0, 10, 19, 20, 21, 24], "domain": [0, 3, 4, 7, 16, 19, 24], "random": [0, 7, 16, 19, 20, 21, 24], "paramet": [0, 4, 6, 10, 11, 14, 16, 17, 19, 21], "jacobian": [0, 4, 10, 24], "invers": [0, 6, 10, 11, 21, 24], "kinemat": [0, 6, 10, 11, 21], "its": [0, 3, 4, 6, 10, 13, 16, 19, 20, 21], "current": [0, 4, 6, 10, 11, 14, 15, 16, 20, 21, 23, 24], "incarn": 0, "standalon": [0, 24], "directli": [0, 4, 10, 14, 16, 21], "interfac": [0, 20, 24], "robot": [0, 10, 11, 14, 16, 20, 21, 23, 24], "product": [0, 11], "though": [0, 21], "same": [0, 4, 6, 10, 15, 16, 19, 20, 21], "underli": [0, 19, 21], "physx": [0, 2, 3, 6, 10, 14, 15, 19, 20, 21, 24], "engin": [0, 6, 19, 20, 21, 23, 24], "under": [0, 11, 13, 20, 21, 23, 24], "hood": [0, 11, 21], "while": [0, 11, 16, 19, 20, 21], "pixar": 0, "usd": [0, 4], "scene": [0, 4, 11, 16, 20], "descript": [0, 19], "languag": [0, 21], "describ": [0, 11, 14, 16, 19, 21], "setup": [0, 12, 13, 16, 17, 24], "python": [0, 1, 10, 11, 12, 14, 17, 19, 20, 21, 23, 24], "code": [0, 11, 16, 20, 21, 23], "custom": [0, 16, 19, 24], "from": [0, 3, 4, 6, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "format": [0, 4, 6, 14, 20, 21], "render": [0, 3, 4, 6, 11, 14, 16, 20, 24], "rel": [0, 4, 6, 13, 14, 15, 16, 20, 23], "either": [0, 10, 16, 20, 21, 23, 24], "rai": 0, "trace": 0, "more": [0, 6, 11, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24], "sophist": 0, "synthet": 0, "made": [0, 3, 4, 16, 21], "avail": [0, 6, 7, 10, 11, 14, 15, 19, 21, 23, 24], "releas": [0, 4, 10, 12, 20, 21], "platform": 0, "howev": [0, 11, 16, 20, 21, 23], "switch": [0, 6], "over": [0, 16, 19, 20, 21], "being": [0, 11, 13, 16, 21, 23], "dure": [0, 6, 14, 15, 16, 19, 20, 21, 23, 24], "cours": 0, "migrat": [0, 24], "some": [0, 11, 13, 14, 16, 19, 20, 21, 22, 23, 24], "undoubtedli": 0, "chang": [0, 4, 6, 10, 11, 13, 19, 20, 21, 23], "new": [0, 4, 16, 19, 20, 21], "addit": [0, 6, 15, 19, 20, 21, 22, 23, 24], "function": [0, 4, 11, 15, 16, 19, 20, 21, 24], "advanc": [0, 4, 11, 16, 23], "soft": [0, 3, 4, 6, 14, 20, 24], "bodi": [0, 2, 3, 4, 6, 11, 14, 16, 17, 20, 22, 23, 24], "ad": [0, 3, 6, 10, 16, 17, 22, 23, 24], "inform": [0, 4, 6, 10, 14, 16, 19, 20, 21, 24], "transit": 0, "share": [0, 6, 10, 13, 16, 20, 21], "dev": 0, "talk": 0, "forum": 0, "when": [0, 4, 6, 10, 12, 13, 14, 15, 16, 19, 20, 21, 23, 24], "In": [0, 6, 11, 13, 14, 16, 19, 20, 21, 22, 23], "meantim": [0, 20], "we": [0, 10, 11, 13, 14, 19, 20, 21, 22, 24], "invit": 0, "academ": 0, "explor": [0, 20], "potenti": 0, "feedback": [0, 20], "your": [0, 11, 13, 20, 21, 23], "gym": [1, 5, 6, 7, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24], "structur": [1, 4, 5, 11, 16, 19, 21, 23], "enum": [1, 5, 11, 21, 24], "constant": [1, 5, 20], "flag": [1, 4, 5, 6, 14, 16, 19, 21, 24], "gymapi": [2, 3, 4, 6, 10, 11, 14, 15, 16, 19, 20, 21], "invalid_handl": [2, 4, 16], "1": [2, 3, 4, 6, 10, 11, 12, 13, 14, 16, 19, 20, 21, 23], "default": [2, 3, 4, 6, 10, 14, 15, 16, 19, 20, 21, 23, 24], "viewer": [2, 4, 6, 10, 13, 14, 16, 17, 24], "size": [2, 4, 6, 11, 16, 19, 21, 24], "default_viewer_width": 2, "1600": 2, "default_viewer_height": 2, "900": 2, "state_non": 2, "0": [2, 3, 4, 6, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 23], "state_po": [2, 4, 19], "state_vel": [2, 4, 19], "2": [2, 3, 4, 6, 10, 11, 15, 16, 20, 21, 23], "state_al": [2, 4, 11, 19], "3": [2, 4, 6, 10, 13, 16, 21, 23, 24], "rigid": [2, 3, 4, 6, 10, 11, 14, 16, 17, 20, 22, 23, 24], "rigid_body_non": 2, "rigid_body_disable_grav": 2, "rigid_body_disable_simul": 2, "onli": [2, 3, 4, 6, 10, 11, 14, 15, 16, 19, 20, 21, 23, 24], "attractor": [2, 4, 6], "axi": [2, 3, 4, 6, 21], "axis_non": 2, "axis_x": 2, "axis_i": 2, "axis_z": 2, "4": [2, 6, 10, 11, 13, 16, 20, 21, 23], "axis_twist": 2, "8": [2, 10, 13, 16, 20, 24], "axis_swing_1": 2, "16": [2, 6, 16], "axis_swing_2": 2, "32": [2, 16, 21], "axis_transl": 2, "7": [2, 4, 6, 10, 13, 21], "axis_rot": 2, "56": 2, "axis_al": 2, "63": 2, "class": [3, 4, 6, 14, 24], "isaacgym": [3, 4, 6, 13, 14, 15, 20, 21, 22, 24], "simtyp": [3, 4], "backend": [3, 4, 10, 15, 19, 20, 21, 23, 24], "type": [3, 4, 6, 10, 11, 15, 19, 20, 21, 22, 23, 24], "member": 3, "sim_physx": [3, 20, 21], "sim_flex": [3, 4, 20], "flex": [3, 4, 6, 10, 14, 19, 20, 24], "upaxi": [3, 10], "up": [3, 4, 6, 12, 15, 19, 21, 23, 24], "up_axis_i": 3, "y": [3, 4, 6, 10, 16, 19, 20, 21], "point": [3, 6, 13, 16, 23], "up_axis_z": [3, 10, 20], "z": [3, 4, 6, 10, 16, 19, 20, 24], "contactcollect": [3, 21], "contact": [3, 4, 6, 14, 15, 16, 17, 23, 24], "collect": [3, 4, 6, 14, 19, 20, 21, 24], "mode": [3, 4, 6, 10, 13, 20, 21, 24], "cc_never": [3, 21], "don": [3, 11, 19, 21, 24], "t": [3, 6, 11, 19, 20, 21, 24], "ani": [3, 14, 16, 20, 21, 24], "valu": [3, 4, 6, 11, 14, 15, 16, 19, 20, 21, 23, 24], "cc_last_substep": [3, 21, 24], "last": [3, 6, 15, 16, 19, 20, 21], "substep": [3, 4, 6, 12, 20, 21, 23, 24], "cc_all_substep": [3, 21, 24], "all": [3, 4, 6, 10, 11, 13, 14, 16, 19, 20, 23, 24], "jointtyp": [3, 4], "joint": [3, 4, 6, 12, 13, 14, 17, 20, 21, 23, 24], "joint_invalid": 3, "invalid": [3, 4], "unknown": 3, "uniniti": 3, "joint_fix": 3, "fix": [3, 4, 6, 13, 16, 19, 21, 23], "move": [3, 10, 12, 13, 16, 19, 21, 23, 24], "togeth": [3, 16, 19, 20, 21], "joint_revolut": 3, "revolut": [3, 4, 6, 19, 21], "hing": 3, "rotat": [3, 4, 6, 16, 20, 21], "one": [3, 4, 10, 12, 13, 14, 15, 16, 19, 20, 21, 23, 24], "defin": [3, 4, 6, 11, 14, 15, 19, 20, 21, 23, 24], "joint_prismat": 3, "prismat": [3, 4, 6, 10, 19, 21], "linearli": 3, "joint_bal": 3, "ball": [3, 10, 23, 24], "direct": [3, 4, 6, 14, 16, 24], "refer": [3, 4, 12, 13, 14, 15, 16, 17, 19, 20, 21, 24], "joint_planar": 3, "planar": [3, 19], "plane": [3, 4, 6, 14, 15, 16, 22, 24], "joint_float": 3, "float": [3, 4, 6, 11, 15, 16, 19, 21], "No": [3, 13], "constraint": [3, 4, 6, 11, 21, 23], "between": [3, 4, 6, 10, 12, 14, 16, 19, 20, 21, 23, 24], "doftyp": [3, 4], "degre": [3, 4, 6, 10, 13, 14, 15, 20], "freedom": [3, 4, 6, 10, 13, 14, 15, 20], "dof_invalid": 3, "dof": [3, 4, 6, 11, 14, 15, 20, 24], "dof_rot": 3, "The": [3, 4, 6, 7, 10, 11, 12, 13, 14, 15, 16, 19, 21, 22, 23, 24], "correspond": [3, 11, 13, 15, 16, 19, 20, 21], "dof_transl": 3, "translat": [3, 21], "dofdrivemod": [3, 19], "possibl": [3, 11, 19, 20, 21, 23, 24], "drive": [3, 10, 15, 20, 21], "control": [3, 6, 14, 16, 17, 20, 23, 24], "actor": [3, 4, 6, 12, 15, 16, 17, 24], "set": [3, 4, 6, 10, 11, 12, 14, 15, 16, 19, 20, 21, 23, 24], "specif": [3, 6, 10, 11, 16, 19, 20, 21, 23, 24], "ignor": [3, 19, 21], "command": [3, 8, 13, 20], "dof_mode_non": [3, 19], "free": [3, 4, 21], "without": [3, 4, 14, 16, 19, 20, 21, 24], "dof_mode_po": [3, 19, 21], "respond": 3, "target": [3, 4, 6, 10, 11, 19, 20, 21, 24], "dof_mode_vel": [3, 19, 21], "dof_mode_effort": [3, 19, 21], "effort": [3, 4, 10, 11, 19, 21], "tendontyp": 3, "tendon": [3, 4, 6, 19, 24], "tendon_fix": 3, "tendon_spati": 3, "spatial": [3, 19, 24], "tensordatatyp": 3, "dtype_float32": 3, "float32": [3, 4, 15, 19, 21], "dtype_uint32": 3, "uint32": [3, 4], "dtype_uint64": 3, "uint64": 3, "dtype_uint8": 3, "uint8": [3, 4], "dtype_int16": 3, "int16": [3, 4], "coordinatespac": [3, 4, 21, 24], "coordin": [3, 4, 6, 16, 19, 20, 21, 24], "env_spac": [3, 4, 20, 21], "local_spac": [3, 21], "global_spac": [3, 21], "meshtyp": [3, 4], "mesh": [3, 4, 6, 10, 16, 20, 22, 23, 24], "mesh_non": 3, "mesh_collis": [3, 16], "collis": [3, 4, 6, 14, 16, 19, 20, 21, 23, 24], "check": [3, 4, 13, 23, 24], "inertia": [3, 4, 6, 14, 24], "For": [3, 4, 11, 14, 15, 16, 19, 20, 21, 23, 24], "improv": [3, 6, 11, 21, 22, 23], "perform": [3, 4, 6, 10, 14, 15, 16, 19, 20, 21, 23, 24], "should": [3, 4, 6, 11, 13, 16, 19, 20, 21, 23, 24], "approxim": [3, 6, 11, 14], "volum": 3, "coars": 3, "convex": [3, 6, 23, 24], "mesh_visu": [3, 16], "visual": [3, 4, 6, 7, 14, 17, 20, 24], "purpos": [3, 7, 16, 23], "mesh_visual_and_collis": [3, 16], "both": [3, 4, 10, 11, 14, 16, 19, 20, 21, 24], "imagetyp": [3, 4], "imag": [3, 4, 6, 10, 13, 20, 24], "gener": [3, 6, 10, 11, 14, 15, 16, 21, 22, 23], "image_color": [3, 16], "rgb": [3, 4, 16], "regular": [3, 21], "camera": [3, 4, 6, 11, 12, 17, 20, 24], "would": [3, 11, 13, 16, 21], "each": [3, 4, 6, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "pixel": [3, 6, 16], "three": [3, 10, 15, 16, 20, 21], "select": [3, 4, 6, 10, 13, 16, 20, 21, 24], "gymtensordatatyp": 3, "repres": [3, 4, 6, 7, 14, 15, 20, 21, 23], "intens": [3, 4, 11, 13, 16], "red": [3, 16], "green": [3, 16], "blue": [3, 16], "image_depth": [3, 16], "depth": [3, 4, 14, 16], "how": [3, 6, 10, 12, 13, 15, 16, 20, 21, 23, 24], "far": [3, 6, 16], "image_segment": [3, 16], "segment": [3, 4, 6], "integ": [3, 6, 11, 16, 20, 21, 24], "object": [3, 4, 6, 10, 11, 13, 14, 20, 21, 23], "displai": [3, 4, 13, 20], "image_optical_flow": [3, 16], "optic": [3, 16], "flow": [3, 16], "2d": [3, 20], "vector": [3, 4, 6, 11, 16, 20, 21, 24], "screen": [3, 16, 20], "space": [3, 4, 6, 16, 20, 21, 24], "visibl": [3, 13, 16], "indexdomain": [3, 4], "index": [3, 4, 6, 12, 15, 16, 19, 20, 21], "compon": [3, 6, 14, 17], "buffer": [3, 4, 6, 10, 16, 19, 20, 21, 24], "domain_actor": [3, 4, 19], "domain_env": [3, 4, 19], "domain_sim": [3, 4, 19, 21], "softmaterialtyp": [3, 6], "materi": [3, 4, 6, 14, 16, 20, 24], "mat_corot": 3, "lagrang": [3, 6, 23], "co": 3, "formul": [3, 21], "finit": 3, "element": [3, 4, 6, 16, 21], "mat_neohookean": 3, "neo": 3, "hookean": 3, "camerafollowmod": [3, 4], "follow": [3, 4, 7, 10, 13, 16, 19, 20, 21, 23], "follow_posit": [3, 16], "attach": [3, 4, 10, 14, 15, 16, 20, 24], "follow_transform": [3, 16], "transform": [3, 4, 6, 11, 15, 16, 19, 20, 24], "orient": [3, 6, 10, 11, 19, 20, 21], "keyboardinput": [3, 4], "key_spac": [3, 20], "key_apostroph": 3, "key_comma": 3, "key_minu": 3, "key_period": 3, "key_slash": 3, "key_0": 3, "key_1": 3, "key_2": 3, "key_3": 3, "key_4": 3, "key_5": 3, "key_6": 3, "key_7": 3, "key_8": 3, "key_9": 3, "key_semicolon": 3, "key_equ": 3, "key_a": 3, "key_b": 3, "key_c": 3, "key_d": 3, "key_": 3, "key_f": 3, "key_g": 3, "key_h": 3, "key_i": 3, "key_j": 3, "key_k": 3, "key_l": 3, "key_m": 3, "key_n": 3, "key_o": 3, "key_p": 3, "key_q": 3, "key_r": [3, 20], "key_t": 3, "key_u": 3, "key_v": 3, "key_w": 3, "key_x": 3, "key_z": 3, "key_left_bracket": 3, "key_backslash": 3, "key_right_bracket": 3, "key_grave_acc": 3, "key_escap": 3, "key_tab": 3, "key_ent": 3, "key_backspac": 3, "key_insert": 3, "key_del": 3, "key_right": 3, "key_left": 3, "key_down": 3, "key_up": 3, "key_page_up": 3, "key_page_down": 3, "key_hom": 3, "key_end": 3, "key_caps_lock": 3, "key_scroll_lock": 3, "key_num_lock": 3, "key_print_screen": 3, "key_paus": 3, "key_f1": 3, "key_f2": 3, "key_f3": 3, "key_f4": 3, "key_f5": 3, "key_f6": 3, "key_f7": 3, "key_f8": 3, "key_f9": 3, "key_f10": 3, "key_f11": 3, "key_f12": 3, "key_numpad_0": 3, "key_numpad_1": 3, "key_numpad_2": 3, "key_numpad_3": 3, "key_numpad_4": 3, "key_numpad_5": 3, "key_numpad_6": 3, "key_numpad_7": 3, "key_numpad_8": 3, "key_numpad_9": 3, "key_numpad_del": 3, "key_numpad_divid": 3, "key_numpad_multipli": 3, "key_numpad_subtract": 3, "key_numpad_add": 3, "key_numpad_ent": 3, "key_numpad_equ": 3, "key_left_shift": 3, "key_left_control": 3, "key_left_alt": 3, "key_left_sup": 3, "key_right_shift": 3, "key_right_control": 3, "key_right_alt": 3, "key_right_sup": 3, "key_menu": 3, "mouseinput": [3, 4], "mouse_left_button": [3, 20], "mouse_right_button": 3, "mouse_middle_button": 3, "mouse_forward_button": 3, "mouse_back_button": 3, "mouse_scroll_right": 3, "mouse_scroll_left": 3, "mouse_scroll_up": 3, "mouse_scroll_down": 3, "mouse_move_right": 3, "mouse_move_left": 3, "mouse_move_up": 3, "mouse_move_down": 3, "acquire_actor_root_state_tensor": [4, 21], "self": [4, 6, 14, 20, 23], "arg0": [4, 6], "sim": [4, 11, 12, 14, 15, 16, 19, 20, 21, 23, 24], "retriev": [4, 10, 11, 13, 15, 16, 21], "root": [4, 13, 14, 19, 20, 24], "ha": [4, 10, 11, 14, 16, 19, 20, 21, 23, 24], "shape": [4, 6, 10, 14, 15, 16, 19, 20, 21, 23, 24], "num_actor": [4, 21], "13": [4, 21], "contain": [4, 6, 10, 14, 16, 20, 21], "linear": [4, 6, 10, 16, 19, 21, 23], "10": [4, 6, 10, 14, 21], "angular": [4, 6, 11, 19, 21], "param1": [4, 6], "handl": [4, 6, 10, 12, 16, 19, 20, 21, 23, 24], "return": [4, 6, 15, 16, 19, 20, 21, 24], "gymtensor": 4, "acquire_dof_force_tensor": [4, 15], "One": [4, 6, 21, 23], "per": [4, 6, 10, 11, 16, 19, 20, 21, 23, 24], "acquire_dof_state_tensor": [4, 21], "num_dof": [4, 19, 21], "acquire_force_sensor_tensor": [4, 15], "num_force_sensor": [4, 15], "6": [4, 6, 10, 13, 15, 20, 21, 23], "acquire_jacobian_tensor": [4, 21], "arg1": [4, 6], "str": 4, "param2": 4, "name": [4, 13, 14, 19, 20, 21, 23, 24], "acquire_mass_matrix_tensor": [4, 21], "mass": [4, 6, 14, 15, 19, 24], "matrix": [4, 6, 10, 16, 21, 24], "acquire_net_contact_force_tensor": [4, 21, 22], "net": [4, 15, 17, 21, 24], "contract": 4, "num_rigid_bodi": [4, 21], "x": [4, 6, 11, 16, 19, 20], "acquire_particle_state_tensor": 4, "particl": [4, 6, 11, 14, 20, 23], "acquire_pneumatic_pressure_tensor": 4, "penumat": 4, "pressur": [4, 24], "pneumat": [4, 24], "acquire_pneumatic_target_tensor": 4, "acquire_rigid_body_state_tensor": [4, 21], "add_ground": [4, 20], "param": 4, "planeparam": [4, 6, 20], "none": [4, 13, 21], "add": [4, 6, 14, 16, 19, 20, 21], "ground": [4, 6, 14, 15, 16, 22, 24], "add_heightfield": 4, "numpi": [4, 6, 10, 11, 13, 15, 16, 19, 20], "ndarrai": [4, 6], "arg2": [4, 6], "heightfieldparam": [4, 6], "heightfield": [4, 6, 10, 22, 24], "heightsampl": 4, "height": [4, 6, 14, 16, 20, 23], "sampl": [4, 6, 10, 14, 16, 19, 21, 24], "arrai": [4, 11, 15, 16, 19, 20, 21], "column": [4, 6, 10, 21], "major": [4, 6], "order": [4, 11, 16, 20, 21], "param3": 4, "carbongym": [4, 24], "gymheightfieldparam": 4, "add_lin": [4, 16], "arg": 4, "kwarg": 4, "overload": 4, "env": [4, 6, 11, 13, 15, 16, 19, 20, 21, 24], "int": [4, 16], "arg3": 4, "arg4": 4, "line": [4, 8, 17, 20, 23], "start": [4, 6, 16, 20, 23], "specifi": [4, 6, 10, 11, 14, 15, 16, 19, 20, 21, 23, 24], "vertic": [4, 6, 16, 23], "color": [4, 7, 10, 20], "tupl": [4, 6], "organ": [4, 20], "p1": 4, "p2": 4, "frame": [4, 6, 10, 11, 15, 16, 19, 20, 21], "rang": [4, 6, 10, 13, 15, 19, 20, 21, 24], "r": [4, 6, 10, 16, 19, 20], "g": [4, 15, 16, 20, 23], "b": [4, 6, 16], "spectrum": 4, "number": [4, 6, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "draw": [4, 10, 16], "param4": 4, "must": [4, 11, 16, 19, 20, 21], "least": 4, "numlin": 4, "param5": 4, "vec3": [4, 6, 15, 16, 19, 20], "add_triangle_mesh": [4, 10, 22], "trianglemeshparam": [4, 6], "apply_actor_dof_effort": [4, 11, 19], "bool": [4, 19], "pass": [4, 6, 10, 13, 14, 15, 16, 19, 20, 21], "If": [4, 6, 11, 13, 14, 16, 19, 20, 21, 23], "newton": [4, 6, 19, 21, 23], "nm": [4, 11, 19, 21], "see": [4, 6, 9, 11, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24], "set_dof_actuation_force_tensor": [4, 21], "version": [4, 6, 13, 24], "obj": [4, 14, 24], "list": [4, 8, 13, 19, 20, 21, 23], "true": [4, 6, 11, 14, 15, 16, 19, 20, 21], "close": [4, 6, 20], "fals": [4, 6, 14, 15, 21, 24], "otherwis": [4, 6], "apply_body_force_at_po": [4, 24], "rigidhandl": 4, "po": [4, 6, 19], "given": [4, 6, 10, 14, 21], "immedi": [4, 16, 21], "timestep": [4, 22, 23], "apply_body_forc": [4, 24], "respect": [4, 6, 10, 11, 14, 15], "apply_dof_effort": 4, "apply_rigid_body_force_at_pos_tensor": [4, 21, 24], "forcetensor": 4, "postensor": 4, "com": [4, 6, 9, 13, 22, 23, 24], "oper": [4, 19, 21, 24], "wa": [4, 11, 13, 15, 19, 21, 24], "succes": 4, "apply_rigid_body_force_tensor": [4, 21, 24], "torquetensor": 4, "attach_camera_to_bodi": [4, 16, 24], "attach_sim": 4, "updat": [4, 6, 10, 13, 16, 19, 20, 21, 24], "save": [4, 10, 11, 19, 20, 21, 23], "stage": [4, 6], "cuda": [4, 6, 10, 21, 23, 24], "enabl": [4, 6, 10, 14, 15, 16, 20, 24], "string": 4, "path": [4, 14, 20, 24], "directori": [4, 13, 14, 20], "filenam": 4, "begin_aggreg": [4, 19], "creat": [4, 6, 10, 11, 13, 15, 16, 17, 21, 23, 24], "aggreg": [4, 21, 24], "group": [4, 10, 20], "maximum": [4, 6, 11, 19, 23], "disabl": [4, 6, 14, 23, 24], "clear_lin": [4, 16], "clear": 4, "create_actor": [4, 15, 19, 20, 21], "asset": [4, 6, 8, 11, 12, 13, 15, 16, 17, 19, 21, 24], "pose": [4, 6, 10, 12, 14, 15, 16, 19, 20, 21], "filter": [4, 6, 14, 20, 22, 24], "segmentationid": [4, 6], "where": [4, 6, 10, 13, 14, 16, 19, 20, 21, 23, 24], "initi": [4, 6, 20, 21, 23], "part": [4, 16], "collid": [4, 6, 10, 20, 22], "anyth": [4, 14], "outsid": [4, 23], "collisiongroup": 4, "param6": 4, "bitwis": [4, 6], "mask": [4, 13, 20], "off": [4, 10, 11, 16, 20], "param7": 4, "id": [4, 20], "create_aggreg": 4, "pipelin": [4, 10, 14, 19, 21, 24], "create_asset_force_sensor": [4, 15, 22], "body_idx": [4, 15], "local_pos": 4, "prop": [4, 19], "forcesensorproperti": [4, 6, 15, 24], "local": [4, 6, 15, 16, 20, 23, 24], "properti": [4, 6, 15, 17, 20, 24], "option": [4, 8, 13, 16, 17, 20, 21, 24], "failur": 4, "create_box": [4, 14], "width": [4, 6, 14, 16], "assetopt": [4, 6, 14, 20, 24], "box": [4, 10, 14, 20, 23, 24], "create_camera_sensor": [4, 16], "cameraproperti": [4, 6, 16, 20], "ar": [4, 6, 7, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "camprop": 4, "create_capsul": [4, 14], "radiu": [4, 14, 23], "capsul": [4, 6, 14, 23, 24], "extend": [4, 24], "along": [4, 6, 21], "origin": [4, 6, 10, 14, 15, 16, 19, 20, 21], "create_cloth_grid": 4, "cloth": 4, "grid": [4, 20], "connect": [4, 6, 16, 20, 21, 23, 24], "length": [4, 6, 11, 14, 19], "distanc": [4, 6, 16, 20, 23], "wise": 4, "create_env": [4, 19, 20], "lower": [4, 19, 20, 23, 24], "bound": 4, "upper": [4, 19, 20], "tile": 4, "row": [4, 6, 10, 20, 21], "create_performance_tim": 4, "timer": [4, 6], "queri": [4, 6, 10, 15, 16, 20, 21, 23], "create_rigid_body_attractor": 4, "attractorproperti": [4, 6], "create_sim": [4, 20, 21], "compute_devic": 4, "graphics_devic": 4, "simparam": [4, 6, 20, 21, 23, 24], "0x7fb9865855e0": 4, "alloc": [4, 16], "which": [4, 6, 10, 11, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24], "devic": [4, 6, 10, 11, 13, 16, 19, 20, 21, 24], "create_spher": [4, 14], "sphere": [4, 14, 23], "create_tet_grid": 4, "softmateri": [4, 6], "arg5": 4, "arg6": 4, "arg7": 4, "arg8": 4, "arg9": 4, "arg10": 4, "arg11": 4, "arg12": 4, "tetrahedr": 4, "definit": [4, 6, 24], "dimx": 4, "tetrahedron": [4, 6], "dimi": 4, "dimz": 4, "spacingx": 4, "spacingi": 4, "param8": 4, "spacingz": 4, "param9": 4, "densiti": [4, 6, 10, 14, 20], "param10": 4, "param11": 4, "top": [4, 6, 13, 16, 20], "param12": 4, "left": [4, 6, 10, 11, 20], "side": [4, 16, 20], "param13": 4, "right": [4, 6, 10, 16, 20], "param14": 4, "create_texture_from_buff": [4, 16], "load": [4, 6, 10, 13, 16, 17, 19, 24], "textur": [4, 8, 10, 14, 24], "py": [4, 13, 14, 18, 19, 20, 21, 22], "array_t": 4, "create_texture_from_fil": [4, 16], "create_usd_export": 4, "usdexportopt": 4, "usdexport": 4, "destroi": [4, 16], "export": [4, 13], "create_view": [4, 16, 20], "debug_print_asset": 4, "outpath": 4, "output": [4, 6, 13, 16], "std": [4, 10], "out": [4, 10, 20, 21], "destroy_camera_sensor": 4, "destroy_env": 4, "delet": [4, 14], "destroy_performance_tim": 4, "intern": [4, 6, 15, 19, 20, 21, 23], "destroy_sim": [4, 20], "clean": 4, "remain": [4, 19, 21], "destroy_usd_export": 4, "destroy_view": [4, 20], "draw_env_rigid_contact": 4, "scale": [4, 6, 11, 17, 24], "squar": 4, "draw_env_soft_contact": 4, "draw_view": [4, 16, 20], "render_collis": 4, "rentercollisionmesh": 4, "determin": [4, 6, 11, 14, 15, 16, 19, 20, 23, 24], "instead": [4, 6, 13, 14, 20, 21, 23, 24], "enable_actor_dof_force_sensor": [4, 15], "end_access_image_tensor": [4, 16], "termin": [4, 20, 23], "end_aggreg": [4, 19], "export_usd_asset": 4, "export_usd_sim": 4, "dirnam": 4, "map": [4, 21], "fail": [4, 23], "fetch_result": [4, 11, 16, 20], "popul": [4, 20, 21], "host": [4, 19, 21, 24], "wait": [4, 11, 20], "latest": [4, 15, 20, 21], "step": [4, 6, 11, 15, 17, 19, 20, 21, 23, 24], "complet": [4, 16, 19, 22], "find_actor_actuator_index": 4, "get": [4, 6, 11, 15, 19, 20, 21, 24], "actuat": [4, 6, 19, 21], "find_actor_dof_handl": 4, "find": 4, "find_actor_dof_index": [4, 19, 21], "eactordomain": 4, "like": [4, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "get_actor_dof_st": [4, 19, 21], "get_actor_dof_properti": [4, 19], "becaus": [4, 10, 11, 13, 14, 20, 21], "deal": [4, 11], "level": [4, 10], "futur": [4, 11, 12, 16, 19, 20, 21, 22, 24], "find_actor_fixed_tendon_joint_index": 4, "differ": [4, 10, 12, 13, 14, 15, 16, 20, 21, 22, 23, 24], "found": [4, 13, 16, 20, 22, 24], "find_actor_handl": 4, "find_actor_index": 4, "find_actor_joint_handl": 4, "find_actor_joint_index": 4, "find_actor_rigid_body_handl": [4, 11], "find_actor_rigid_body_index": [4, 19, 21], "get_env_rigid_body_st": [4, 19], "get_sim_rigid_body_st": [4, 19], "get_actor_rigid_body_st": [4, 11, 19, 21], "get_actor_rigid_body_properti": 4, "find_actor_tendon_index": 4, "find_asset_actuator_index": 4, "find_asset_dof_index": 4, "find_asset_joint_index": 4, "find_asset_rigid_body_index": [4, 15], "find_asset_tendon_index": 4, "free_textur": [4, 16], "get_actor_actuator_count": 4, "get_actor_actuator_joint_nam": 4, "get_actor_actuator_nam": 4, "get_actor_actuator_properti": 4, "actuatorproperti": 4, "get_actor_asset": 4, "get_actor_count": 4, "get_actor_dof_count": [4, 19, 21], "get_actor_dof_dict": 4, "dict": 4, "indic": [4, 10, 16, 21], "dictionari": [4, 21], "get_actor_dof_forc": [4, 15], "depend": [4, 11, 13, 14, 19, 20, 21], "dof_force_sensor_mod": 4, "get_actor_dof_fram": 4, "doffram": [4, 6], "get_actor_dof_handl": 4, "get_actor_dof_index": [4, 21], "get_actor_dof_nam": 4, "freedon": 4, "get_actor_dof_position_target": 4, "meter": [4, 6, 16, 19, 21, 23], "radian": [4, 6, 16, 19, 21], "carb": 4, "gymdofproperti": 4, "dofstat": [4, 6], "get_actor_dof_velocity_target": 4, "m": [4, 6, 23], "rad": [4, 6], "get_actor_fixed_tendon_joint_coeffici": 4, "coeffici": [4, 6, 20], "get_actor_fixed_tendon_joint_nam": 4, "get_actor_force_sensor": [4, 15], "forcesensor": 4, "instanc": [4, 10, 14, 15, 19, 20, 21], "get_actor_force_sensor_count": [4, 15], "get_actor_handl": 4, "get_actor_index": [4, 21], "get_actor_joint_count": [4, 19], "get_actor_joint_dict": 4, "get_actor_joint_handl": 4, "get_actor_joint_index": 4, "get_actor_joint_nam": 4, "get_actor_joint_transform": 4, "get_actor_nam": 4, "get_actor_rigid_body_count": [4, 19, 21], "get_actor_rigid_body_dict": 4, "get_actor_rigid_body_handl": 4, "get_actor_rigid_body_index": [4, 21], "get_actor_rigid_body_nam": 4, "rigidbodyproperti": [4, 6], "get_actor_rigid_body_shape_indic": 4, "indexrang": [4, 6], "rigidbodyst": [4, 6], "obtain": [4, 15, 19, 20, 21], "get_actor_rigid_shape_count": 4, "count": [4, 6, 19, 21, 23], "get_actor_rigid_shape_properti": 4, "rigidshapeproperti": [4, 6], "get_actor_root_rigid_body_handl": 4, "get_actor_scal": 4, "factor": [4, 6, 10, 11, 23], "get_actor_soft_body_count": 4, "get_actor_soft_materi": 4, "get_actor_tendon_count": 4, "get_actor_tendon_nam": 4, "get_actor_tendon_offset": 4, "offset": [4, 6, 16, 19, 21, 23, 24], "float_max": 4, "get_actor_tendon_properti": 4, "tendonproperti": 4, "get_actor_tetrahedra_rang": 4, "tetrahedra": 4, "link": [4, 6, 21, 24], "get_actor_triangle_rang": 4, "triangl": [4, 6, 10, 14, 22, 23, 24], "get_asset_actuator_count": 4, "get_asset_actuator_joint_nam": 4, "get_asset_actuator_nam": 4, "get_asset_actuator_properti": 4, "get_asset_dof_count": [4, 21], "get_asset_dof_dict": [4, 21], "get_asset_dof_nam": 4, "get_asset_dof_properti": [4, 19], "get_asset_dof_typ": 4, "get_asset_fixed_tendon_joint_coeffici": 4, "get_asset_fixed_tendon_joint_nam": 4, "get_asset_joint_count": 4, "get_asset_joint_dict": 4, "get_asset_joint_nam": 4, "get_asset_joint_typ": 4, "get_asset_rigid_body_count": [4, 19], "get_asset_rigid_body_dict": [4, 21], "get_asset_rigid_body_nam": 4, "get_asset_rigid_body_shape_indic": 4, "i": [4, 6, 10, 12, 15, 16, 19, 20, 21], "e": [4, 10, 11, 13, 15, 16, 20, 21, 23], "get_asset_rigid_shape_count": [4, 19], "get_asset_rigid_shape_properti": 4, "get_asset_soft_body_count": 4, "get_asset_soft_materi": 4, "get_asset_tendon_count": 4, "get_asset_tendon_nam": 4, "get_asset_tendon_properti": 4, "get_attractor_properti": 4, "get_camera_imag": [4, 16], "get_camera_image_gpu_tensor": [4, 16], "get_camera_proj_matrix": [4, 16], "project": [4, 6, 16, 24], "mat44": [4, 6], "get_camera_transform": 4, "refenv": 4, "get_camera_view_matrix": [4, 16], "view": [4, 6, 10, 14, 16, 20, 21, 24], "get_dof_fram": 4, "get_dof_posit": 4, "get_dof_target_posit": 4, "set_dof_position_target_tensor": [4, 21], "get_dof_target_veloc": 4, "get_dof_type_str": 4, "convert": [4, 10, 14, 16, 21, 22], "gymdoftyp": 4, "get_dof_veloc": 4, "get_elapsed_tim": 4, "elaps": [4, 11, 20], "wall": 4, "clock": 4, "time": [4, 6, 10, 11, 14, 16, 19, 20, 21, 23], "sinc": [4, 6, 14, 16, 21], "second": [4, 6, 10, 11, 13, 16, 19, 20, 21, 23], "doubl": 4, "get_env": 4, "valid": [4, 10, 16, 21], "nullptr": 4, "get_env_count": 4, "get_env_dof_count": 4, "get_env_joint_count": 4, "get_env_origin": 4, "global": [4, 6, 16, 20, 21, 24], "get_env_rigid_body_count": 4, "get_env_rigid_contact_forc": 4, "get_env_rigid_contact": 4, "rigidcontact": [4, 6], "get_frame_count": 4, "int64_t": 4, "get_joint_handl": 4, "search": [4, 6, 12, 14, 20, 23], "get_joint_nam": 4, "get_joint_posit": 4, "interest": [4, 19], "get_joint_target_posit": 4, "get_joint_target_veloc": 4, "get_joint_transform": 4, "get_joint_type_str": 4, "get_joint_veloc": 4, "get_performance_tim": 4, "performancetim": [4, 6], "struct": 4, "reflect": 4, "most": [4, 10, 11, 16, 20, 21, 23, 24], "recent": 4, "get_pneumatic_pressur": 4, "softactu": 4, "pa": 4, "get_pneumatic_target": 4, "get_rigid_angular_veloc": 4, "get_rigid_body_color": [4, 16], "what": [4, 12, 20, 21, 24], "get_rigid_body_segmentation_id": 4, "uint32_t": 4, "get_rigid_body_textur": [4, 16], "get_rigid_contact_forc": 4, "get_rigid_contact": 4, "get_rigid_handl": 4, "get_rigid_linear_veloc": 4, "get_rigid_nam": 4, "get_rigid_transform": 4, "bind": [4, 11], "get_sensor": 4, "n": [4, 10, 24], "get_sim_actor_count": [4, 21], "total": [4, 11, 15, 19, 20, 21], "get_sim_dof_count": [4, 21], "get_sim_force_sensor_count": [4, 15], "get_sim_joint_count": 4, "get_sim_param": 4, "get_sim_rigid_body_count": [4, 21], "get_sim_tetrahedra": 4, "mat33": [4, 6], "cauchi": 4, "stress": [4, 10, 24], "matrix33": 4, "get_sim_tetrahedra_count": 4, "get_sim_tim": 4, "get_sim_triangle_count": 4, "deform": [4, 23], "get_sim_triangl": 4, "parent": [4, 15], "face": [4, 6, 14, 24], "normal": [4, 6, 10, 14, 16, 20, 24], "float3": 4, "get_soft_contact": 4, "softcontact": 4, "get_usd_export_root": 4, "get_vec_actor_dof_st": 4, "multipl": [4, 6, 12, 13, 14, 15, 19, 20, 21, 24], "get_vec_env_rigid_contact_forc": 4, "numenv": 4, "numbodi": 4, "get_vec_rigid_angular_veloc": 4, "get_vec_rigid_linear_veloc": 4, "get_vec_rigid_transform": 4, "get_vers": 4, "get_viewer_camera_handl": 4, "get_viewer_camera_transform": 4, "revenv": 4, "get_viewer_mouse_posit": 4, "vec2": 4, "mous": [4, 24], "get_viewer_s": 4, "int2": 4, "window": [4, 10, 20, 24], "load_asset": [4, 14, 20], "rootpath": 4, "extens": [4, 14, 20, 23], "folder": [4, 24], "load_mjcf": 4, "load_opensim": 4, "opensim": 4, "load_sim": 4, "load_urdf": 4, "load_usd": 4, "omni_connect": 4, "omniconnectionparam": 4, "omnivers": [4, 9, 12, 13, 22, 24], "kit": 4, "omni_disconnect": 4, "disconnect": 4, "poll_viewer_ev": 4, "poll": 4, "prepare_sim": [4, 21], "prepar": [4, 24], "query_viewer_action_ev": [4, 20], "actionev": [4, 6], "event": [4, 20], "occur": [4, 11, 21, 23], "query_viewer_has_clos": [4, 16, 20], "whether": [4, 6, 10, 16, 19, 21], "refresh_actor_root_state_tensor": [4, 21], "refresh_dof_force_tensor": [4, 15], "refresh_dof_state_tensor": [4, 21], "refresh_force_sensor_tensor": [4, 15], "refresh_jacobian_tensor": [4, 21], "refresh_mass_matrix_tensor": [4, 21], "refresh_net_contact_force_tensor": [4, 21], "refresh_particle_state_tensor": 4, "refresh_pneumatic_pressure_tensor": 4, "refresh_pneumatic_target_tensor": 4, "refresh_rigid_body_state_tensor": [4, 21], "render_all_camera_sensor": [4, 16], "reset_actor_materi": [4, 16], "reset": [4, 10, 11, 19, 20, 21, 24], "reset_actor_particles_to_rest": 4, "rest": [4, 6, 15, 20, 21, 23], "set_actor_dof_position_target": [4, 11, 19], "set_actor_dof_properti": [4, 19, 21], "set_actor_dof_st": [4, 11, 19, 21], "set_dof_state_tensor": [4, 21], "set_actor_dof_velocity_target": [4, 19], "fredom": 4, "set_dof_velocity_target_tensor": [4, 21], "set_actor_fixed_tendon_joint_coeffici": 4, "set_actor_rigid_body_properti": 4, "actorhandl": 4, "recomputeinertia": 4, "note": [4, 10, 12, 13, 15, 19, 20, 21], "recomput": [4, 14, 24], "set_actor_rigid_body_st": [4, 11, 19, 21], "set_rigid_body_state_tensor": 4, "set_actor_rigid_shape_properti": 4, "ector": 4, "set_actor_root_state_tensor": [4, 21], "set_actor_root_state_tensor_index": [4, 21], "full": [4, 13, 19, 21], "set_actor_scal": 4, "successfulli": 4, "set_actor_soft_materi": 4, "set_actor_tendon_offset": 4, "success": 4, "set_actor_tendon_properti": 4, "set_asset_fixed_tendon_joint_coeffici": 4, "set_asset_rigid_shape_properti": 4, "set_asset_tendon_properti": 4, "set_attractor_properti": 4, "modifi": [4, 10, 16, 20, 21], "setattractortarget": 4, "set_attractor_target": 4, "set_camera_loc": [4, 16], "look": [4, 14, 16, 19, 20, 21, 23], "locat": [4, 6, 10, 13, 16, 21], "manual": [4, 11, 14, 16, 19, 20], "detach": 4, "set_camera_transform": [4, 16], "set_dof_actuation_force_tensor_index": [4, 21], "presimat": 4, "set_dof_position_target_tensor_index": [4, 21], "set_dof_state_tensor_index": [4, 21], "set_dof_target_posit": 4, "set_dof_target_veloc": 4, "set_dof_velocity_target_tensor_index": 4, "set_env_rigid_body_st": [4, 19], "set_joint_target_posit": 4, "set_joint_target_veloc": 4, "set_light_paramet": [4, 16], "light": [4, 10], "focu": [4, 10, 20], "channel": 4, "ambient": [4, 16], "set_particle_state_tensor": 4, "set_particle_state_tensor_index": 4, "set_pneumatic_pressur": 4, "clamp": 4, "max": [4, 6, 16, 19], "set_pneumatic_pressure_tensor": 4, "set_pneumatic_pressure_tensor_index": 4, "set_pneumatic_target": 4, "set_pneumatic_target_tensor": 4, "set_pneumatic_target_tensor_index": 4, "set_rigid_angular_veloc": 4, "set_rigid_body_color": [4, 16], "set_rigid_body_segmentation_id": [4, 16], "set_rigid_body_textur": [4, 16], "set_rigid_linear_veloc": 4, "set_rigid_transform": 4, "set_sim_devic": 4, "comput": [4, 6, 10, 14, 15, 19, 20, 21, 23, 24], "set_sim_param": 4, "set_sim_rigid_body_st": [4, 19], "set_usd_export_root": 4, "work": [4, 10, 13, 14, 19, 20, 21, 23], "dt": [4, 6, 12, 20, 23], "divid": [4, 11], "start_access_image_tensor": [4, 16], "transfer": 4, "step_graph": [4, 16, 20], "graphic": [4, 6, 12, 13, 17, 20, 24], "through": [4, 11, 13, 16, 23], "method": [4, 10, 11, 13, 14, 15, 16, 19, 20, 21, 23, 24], "call": [4, 6, 12, 13, 15, 16, 19, 20, 21, 23, 24], "subscribe_viewer_keyboard_ev": [4, 20], "subscrib": [4, 20], "keyboard": 4, "keep": [4, 6, 21], "push": 4, "input": [4, 16, 24], "subscribe_viewer_mouse_ev": [4, 20], "sync_frame_tim": [4, 11, 20], "throttl": [4, 20], "speed": [4, 10, 14], "real": [4, 11, 20], "viewer_camera_look_at": 4, "write_camera_image_to_fil": 4, "write": [4, 10, 16, 21], "png": [4, 16], "write_viewer_image_to_fil": 4, "minor": 6, "cross": 6, "dot": 6, "dtype": [6, 11, 21], "f4": 6, "static": [6, 10, 20, 22], "from_buff": 6, "length_sq": 6, "quat": [6, 16, 19, 20], "quaternion": [6, 20, 21, 24], "represent": [6, 10, 20, 21, 23], "w": [6, 19, 20], "from_axis_angl": [6, 16, 20], "from_euler_zyx": 6, "to_euler_zyx": 6, "p": [6, 10, 16, 19, 20], "hat": 6, "j": 6, "k": 6, "transform_point": 6, "quatertnion": 6, "transform_vector": 6, "vel": [6, 11, 19], "certain": [6, 11, 13, 20], "behaivor": 6, "bodyflag": 6, "invinertia": 6, "invmass": 6, "kg": 6, "architectur": 6, "hold": [6, 21], "3x3": 6, "inetia": 6, "4x4": 6, "slice": [6, 11, 19, 21], "dynamic_frict": [6, 20], "dynam": [6, 10, 20, 23, 24], "friction": [6, 19, 20, 23, 24], "restitut": [6, 20, 24], "segmentation_id": [6, 16], "truth": [6, 16], "static_frict": [6, 20], "pull": 6, "toward": 6, "individu": [6, 14, 15, 19, 20, 21, 24], "ax": [6, 20, 21, 24], "gymtransformaxesflag": 6, "zero": [6, 11, 14, 15, 16, 19, 20, 21, 23], "won": 6, "impact": [6, 15, 20, 23], "solver": [6, 15, 20, 23], "complex": [6, 11, 23], "damp": [6, 19, 23], "attract": 6, "rigid_handl": 6, "stiff": [6, 19, 23], "larger": [6, 10, 16, 23], "largest": 6, "agent": [6, 20], "chain": 6, "stif": 6, "complianc": 6, "compliant": 6, "smaller": [6, 23], "stronger": 6, "greater": [6, 11, 23], "equal": [6, 11, 23], "contact_offset": [6, 20, 23, 24], "bitmask": 6, "filtera": 6, "filterb": 6, "rest_offset": [6, 20, 23, 24], "come": [6, 14, 20, 23], "surfac": 6, "It": [6, 10, 11, 13, 19, 20, 21, 23, 24], "ratio": 6, "final": [6, 11, 16, 21], "after": [6, 15, 16, 19, 20, 21], "rolling_frict": 6, "roll": 6, "thick": [6, 23, 24], "torsion_frict": 6, "torsion": 6, "enable_constraint_solver_forc": [6, 15], "receiv": 6, "enable_forward_dynamics_forc": [6, 15], "forward": [6, 11], "use_world_fram": [6, 15], "world": [6, 11, 16, 20, 23], "thei": [6, 10, 11, 14, 15, 16, 19, 20, 21, 22, 23], "report": [6, 13, 15, 20, 21, 22, 24], "activ": [6, 13, 16, 20], "fiber": 6, "activationmax": 6, "model": [6, 8, 10, 14, 16, 19, 20, 23], "poisson": 6, "ration": 6, "young": 6, "modulu": 6, "wrapper": [6, 19], "data_address": 6, "address": [6, 11, 19, 21], "data_ptr": 6, "pointer": [6, 11, 16], "ndim": 6, "dimens": [6, 16, 20], "own_data": 6, "ownership": 6, "body0": 6, "body1": 6, "env0": 6, "belong": [6, 20], "unrecogn": 6, "env1": 6, "effect": [6, 19, 20, 21, 23], "pair": [6, 14, 23], "initial_overlap": 6, "amount": [6, 11, 20], "overlap": 6, "lambda": 6, "magnitud": 6, "lambda_frict": 6, "local_pos0": 6, "exclud": 6, "here": [6, 19, 21], "local_pos1": 6, "min_dist": 6, "minimum": [6, 13], "try": [6, 11, 23], "maintain": [6, 16, 19, 20, 23], "two": [6, 10, 11, 15, 16, 20, 21, 23], "offset0": 6, "localpos0": 6, "That": [6, 13], "offset1": 6, "localpos1": 6, "coeffitienc": 6, "flexparam": [6, 20], "contact_regular": 6, "against": [6, 23], "deterministic_mod": 6, "determinist": [6, 24], "friction_mod": 6, "dir": 6, "non": [6, 14, 19, 21, 23, 24], "cone": 6, "solv": 6, "abov": [6, 11, 16, 20, 21, 23], "plu": 6, "spin": 6, "geometric_stiff": 6, "stabil": [6, 10, 14, 20, 23, 24], "hessian": 6, "max_rigid_contact": 6, "max_soft_contact": 6, "num_inner_iter": [6, 20, 23], "inner": [6, 23], "loop": [6, 16, 20, 21, 23], "iter": [6, 16, 20, 23], "taken": [6, 11, 23], "Is": 6, "num_outer_iter": [6, 20, 23], "particle_frict": 6, "relax": [6, 20, 23], "converg": [6, 23], "rate": [6, 11, 16, 20, 23], "parallel": [6, 11, 21, 24], "lead": [6, 16, 21, 23], "instabl": [6, 23, 24], "return_contact": 6, "read": [6, 15, 16, 21, 24], "shape_collision_dist": [6, 23], "shape_collision_margin": [6, 23], "solver_typ": [6, 20, 23], "xpbd": [6, 23], "jacobi": [6, 23], "ldlt": [6, 23], "pcg": 6, "5": [6, 10, 11, 13, 20, 23], "pcr": [6, 23], "gauss": [6, 23], "seidel": [6, 23], "nncg": 6, "warm_start": [6, 20, 23], "fraction": [6, 23], "cach": [6, 11, 14, 19, 20, 21, 23, 24], "multipli": [6, 10, 16, 23, 24], "next": [6, 10, 19, 20, 23], "physxparam": [6, 20], "always_use_articul": 6, "even": [6, 10, 11, 20, 21, 22, 24], "articul": [6, 8, 10, 15, 20, 21, 23, 24], "bounce_threshold_veloc": [6, 23, 24], "below": [6, 7, 16, 19, 20, 23], "bounc": [6, 20, 23], "typic": [6, 19, 20, 23], "about": [6, 11, 12, 16, 20, 21, 24], "graviti": [6, 20], "num_substep": [6, 23], "contact_collect": [6, 21, 24], "whose": [6, 16, 21, 23], "less": [6, 11, 13, 19, 23], "sum": [6, 23], "contactoffset": [6, 23], "default_buffer_size_multipli": 6, "friction_correlation_dist": [6, 23], "correl": [6, 23, 24], "friction_offset_threshold": [6, 23], "threshold": 6, "max_depenetration_veloc": [6, 23, 24], "introduc": [6, 23], "correct": [6, 13, 16, 21, 23], "penetr": [6, 23], "max_gpu_contact_pair": 6, "num_position_iter": [6, 20, 23], "255": [6, 16], "num_subscen": 6, "subscen": [6, 24], "multithread": 6, "num_thread": [6, 10, 23], "thread": [6, 10, 21, 23], "befor": [6, 11, 16, 20, 21], "pxscene": [6, 23], "spawn": [6, 10, 11, 20, 23], "numcor": [6, 23], "worker": [6, 23], "num_velocity_iter": [6, 20, 23], "restoffset": [6, 23], "pg": [6, 23], "sequenti": [6, 21], "impuls": 6, "tg": [6, 23, 24], "robust": [6, 20, 23], "slightli": [6, 21], "expens": [6, 23, 24], "use_gpu": [6, 20, 21], "moment": [6, 10, 22, 23], "enable_actor_creation_warn": 6, "num_client_thread": 6, "stress_visu": 6, "stress_visualization_max": 6, "stress_visualization_min": 6, "up_axi": [6, 20], "use_gpu_pipelin": [6, 21], "angular_damp": 6, "armatur": [6, 14, 19, 20, 23, 24], "diagon": 6, "could": [6, 11, 16, 20, 21, 23, 24], "collapse_fixed_joint": 6, "merg": [6, 23], "convex_decomposition_from_submesh": [6, 14], "treat": [6, 16], "submesh": [6, 14, 16], "decomposit": [6, 24], "default_dof_drive_mod": 6, "drivemodeflag": 6, "disable_grav": 6, "enable_gyroscopic_forc": [6, 24], "gyroscop": [6, 24], "fix_base_link": [6, 14, 20], "placement": 6, "upon": 6, "flip_visual_attach": 6, "hand": [6, 10, 24], "linear_damp": 6, "max_angular_veloc": 6, "max_linear_veloc": 6, "mesh_normal_mod": [6, 14], "from_asset": 6, "compute_per_vertex": [6, 14], "compute_per_fac": [6, 14], "fall": 6, "fulli": [6, 19, 20, 21], "min_particle_mass": 6, "override_com": [6, 14], "geometri": [6, 10, 14, 16, 19, 24], "overrid": [6, 10, 19, 20, 21, 24], "override_inertia": [6, 14], "replace_cylinder_with_capsul": 6, "replac": [6, 16, 24], "cylind": [6, 24], "slices_per_cylind": 6, "bottom": 6, "tendon_limit_stiff": 6, "limit": [6, 11, 12, 16, 17, 19, 20, 23], "choos": [6, 10, 24], "small": [6, 11, 15, 16, 23, 24], "implicitli": [6, 16], "avoid": [6, 11, 14, 19, 20, 21, 23], "oscil": 6, "apporpri": 6, "use_mesh_materi": [6, 10, 14], "use_physx_armatur": 6, "modififc": 6, "vhacd_en": [6, 14], "vhacd_param": [6, 14], "hull": [6, 14], "enable_tensor": [6, 16], "interop": [6, 16, 21, 24], "far_plan": [6, 16], "clip": [6, 16], "horizontal_fov": [6, 16], "horizont": [6, 16], "field": [6, 11, 16, 19], "near_plan": [6, 16], "unit": [6, 11, 16, 21], "supersampling_horizont": [6, 16], "oversampl": 6, "horiziont": 6, "supersampling_vert": [6, 16], "use_collision_geometri": [6, 16], "spent": [6, 20], "do": [6, 12, 13, 19, 20, 21, 23], "frame_idl": 6, "idl": [6, 20], "framer": 6, "graphics_image_retriev": 6, "graphics_sensor_rend": 6, "graphics_viewer_rend": 6, "physics_data_mov": 6, "physics_sim": 6, "total_tim": 6, "vhacdparam": [6, 14], "vhacd": [6, 14], "alpha": [6, 16], "bia": 6, "symmetri": 6, "05": 6, "beta": 6, "concav": 6, "convex_hull_approxim": 6, "convex_hull_downsampl": 6, "precis": [6, 21], "process": [6, 16, 20, 21], "max_convex_hul": [6, 14], "64": [6, 14, 20], "max_num_vertices_per_ch": [6, 14], "1024": 6, "min_volume_per_ch": 6, "adapt": [6, 16, 24], "01": [6, 14, 20, 23], "0001": 6, "voxel": 6, "ocl_acceler": 6, "pca": 6, "plane_downsampl": 6, "granular": 6, "best": [6, 11, 15, 16], "project_hull_vertic": 6, "resolut": [6, 14, 16], "000": 6, "100": [6, 19, 21, 23], "perpendicular": 6, "column_scal": 6, "nbcolumn": 6, "nbrow": 6, "row_scal": 6, "vertical_scal": 6, "nb_triangl": 6, "nb_vertic": 6, "scheme": [7, 20], "background_texture_metal_rust": 7, "jpg": [7, 16], "metal_wall_iron_f": 7, "particle_board_paint_ag": 7, "pebble_stone_texture_natur": 7, "texture_background_wall_paint_2": 7, "texture_background_wall_paint_3": 7, "texture_licens": 7, "txt": 7, "texture_stone_stone_texture_0": 7, "texture_wood_brown_1033760": 7, "program": [8, 11, 12, 24], "common": [8, 11, 16, 17, 20], "bundl": [8, 12], "pleas": [9, 13, 20, 21, 22, 24], "http": [9, 13, 22, 23, 24], "github": [9, 13, 22, 24], "isaacgymenv": [9, 13, 22, 24], "help": [10, 20, 23], "print": [10, 15, 19], "commandlin": 10, "sim_devic": [10, 24], "syntax": [10, 21], "graphics_device_id": [10, 20, 21], "ordin": [10, 20], "except": 10, "claw": 10, "demonstr": [10, 19], "usag": [10, 14, 19, 21], "pyramid": 10, "By": [10, 14, 16, 20, 23, 24], "all_collis": 10, "make": [10, 11, 13, 19, 20, 21, 23], "across": [10, 21], "no_collis": 10, "those": [10, 11, 14, 16, 19, 21], "introspect": [10, 17], "first": [10, 11, 14, 15, 16, 20, 21, 23], "onc": [10, 13, 15, 16, 19, 20, 21, 23], "been": [10, 16, 21, 24], "relat": [10, 12, 16, 21, 24], "variou": [10, 21, 24], "scenario": [10, 20], "present": [10, 11, 16, 20, 21, 23], "detail": [10, 13, 15, 20, 21, 22, 24], "vari": [10, 20, 21], "third": [10, 11, 14, 16, 20], "captur": [10, 19, 20, 21, 23], "viewpoint": 10, "save_imag": 10, "disk": 10, "navig": 10, "arm": [10, 11, 20, 21, 23], "reach": 10, "sever": [10, 20], "At": [10, 16, 19, 20], "cartpol": [10, 13], "enviorn": 10, "anim": [10, 13], "util": [10, 12, 17, 20, 21, 22, 24], "argument": [10, 13, 16, 19, 20, 21, 24], "asset_id": 10, "speed_scal": 10, "show_axi": 10, "convers": [10, 24], "show": [10, 13, 16, 20], "upcom": 10, "cabinet": [10, 11], "wirefram": 10, "helper": [10, 20], "door": 10, "drawer": 10, "illustr": [10, 19], "interact": [10, 13, 19, 20, 21, 22, 23, 24], "kei": [10, 16, 20], "shoot": 10, "perfrom": 10, "stack": 10, "everi": [10, 11, 14, 15, 16, 19, 20, 21, 23], "previou": [10, 16, 20, 21, 24], "inverted_pyramid_test": 10, "heavier": 10, "num_env": [10, 19, 20, 21], "allegro": 10, "num_object": 10, "object_typ": 10, "meat": 10, "banana": 10, "mug": 10, "brick": 10, "press": [10, 19, 24], "c": [10, 20, 21], "turn": [10, 19], "resid": [10, 16, 21], "effector": [10, 11], "independ": [10, 14, 19, 20, 21], "produc": [10, 24], "circular": 10, "motion": [10, 13, 14, 16, 19, 21], "pos_control": 10, "boolvalu": 10, "orn_control": 10, "matric": [10, 24], "yet": [10, 20], "abil": [10, 20], "spam": 10, "vertex": [10, 14, 24], "num_column": 10, "increas": [10, 16, 20, 23, 24], "min_scal": 10, "max_scal": 10, "tool": [10, 21], "random_uniform_terrain": [10, 22], "sloped_terrain": [10, 22], "pyramid_sloped_terrain": [10, 22], "discrete_obstacles_terrain": [10, 22], "wave_terrain": [10, 22], "stairs_terrain": [10, 22], "pyramid_stairs_terrain": [10, 22], "stepping_stones_terrain": [10, 22], "convert_heightfield_to_trimesh": [10, 22], "cuboid": 10, "goal": 10, "isaac": [11, 13, 14, 16, 20, 24], "extrem": 11, "larg": [11, 20, 22, 23], "scope": 11, "There": [11, 13, 14, 16, 19, 20, 21, 23, 24], "great": 11, "go": [11, 20], "so": [11, 13, 14, 19, 20, 21, 23], "reason": [11, 15, 16, 19, 20], "might": [11, 20, 21], "exactli": 11, "thing": [11, 13, 20], "section": [11, 14, 16, 19, 20, 21, 24], "seem": 11, "often": [11, 20, 23], "increment": [11, 20, 23], "via": 11, "mani": [11, 14, 16, 19, 20, 23, 24], "split": [11, 14, 20], "finer": 11, "accuraci": [11, 14, 21], "computation": 11, "becom": [11, 21], "exampl": [11, 12, 16, 17, 18, 19, 20, 21, 22, 23, 24], "sai": [11, 23], "60": [11, 20, 23], "propag": 11, "60th": 11, "120th": 11, "probabl": 11, "consequ": [11, 19], "numer": [11, 23, 24], "integr": [11, 13, 20], "As": [11, 16, 19, 20, 21, 22], "zeroth": 11, "let": [11, 19, 20, 21], "euler": [11, 24], "know": [11, 16], "t_0": 11, "deriv": 11, "estim": 11, "text": 11, "d": [11, 13, 19], "naiv": 11, "approx": 11, "relationship": 11, "exact": [11, 14], "life": [11, 21], "never": [11, 21], "alwai": [11, 14, 15, 16, 19, 21], "form": [11, 20], "error": [11, 13, 21, 23, 24], "suppos": [11, 21], "mean": [11, 13, 14, 16, 19, 20, 21, 23, 24], "our": [11, 14, 20, 21, 22], "71828": 11, "25": 11, "17": [11, 21], "much": [11, 16, 20, 23], "sensit": 11, "chosen": 11, "doesn": [11, 20, 21], "cannot": [11, 13, 14, 16, 20, 21, 23], "escap": 11, "too": [11, 23], "weird": 11, "behavior": [11, 16, 20, 21], "explod": 11, "undesir": [11, 14], "long": [11, 14, 23], "usual": [11, 15, 20, 23], "To": [11, 13, 14, 15, 16, 19, 20, 21, 23], "temporarili": 11, "fetch": 11, "virtual": 11, "longer": [11, 16, 20], "modern": 11, "hardwar": 11, "concern": 11, "practic": [11, 19, 21], "aren": 11, "ocean": 11, "chanc": 11, "high": [11, 23], "issu": [11, 12, 17, 21], "elsewher": 11, "significantli": 11, "fast": [11, 14, 21, 23], "appear": 11, "faster": [11, 20], "mitig": 11, "synchron": [11, 20, 24], "until": [11, 16, 21], "continu": 11, "uniqu": [11, 20], "basi": [11, 23], "awar": [11, 20, 21], "well": [11, 16, 19, 21, 23, 24], "manag": [11, 16, 21], "franka": [11, 19, 20, 21], "panda": 11, "few": [11, 16, 19, 20, 21], "hundr": [11, 20], "want": [11, 13, 16, 21, 23], "need": [11, 13, 14, 15, 16, 19, 20, 21, 23], "easi": [11, 20, 21], "just": [11, 19, 20, 21], "construct": [11, 16, 20, 21], "bit": [11, 16, 19, 20, 21], "trickier": 11, "panda_hand": [11, 21], "effector_handl": 11, "env_ptr": 11, "franka_actor_handl": 11, "short": [11, 23], "wai": [11, 13, 15, 16, 19, 20, 21, 23], "gear": 11, "power": [11, 21], "done": [11, 19, 20, 21], "accept": [11, 16], "match": [11, 13, 24], "articulat": 11, "whatev": 11, "further": [11, 16], "danger": 11, "risk": [11, 13], "ever": [11, 21], "train": [11, 13, 16, 20, 24], "strucur": 11, "gymrigidbodyst": [11, 21], "actual": [11, 20], "gymtransform": [11, 16], "gymveloc": 11, "again": [11, 16], "teleport": [11, 21], "instantan": [11, 21], "later": [11, 19, 20, 21], "veri": [11, 16, 20, 21, 23], "doe": [12, 13, 14, 16, 20, 21], "instal": 12, "prerequisit": 12, "packag": [12, 24], "test": [12, 19, 20], "troubleshoot": 12, "preview4": 12, "preview3": 12, "preview2": 12, "preview1": 12, "known": [12, 13, 19, 22], "tune": [12, 17, 19, 20, 24], "math": [12, 17, 19, 20], "terrain": [12, 17, 24], "frequent": 12, "ask": [12, 16], "question": 12, "happen": 12, "you": [12, 13, 14, 15, 19, 20, 21, 23], "modul": [12, 16, 20, 21, 24], "page": [12, 24], "ubuntu": 13, "18": 13, "04": [13, 23], "20": [13, 20], "driver": 13, "linux": 13, "470": [13, 24], "automat": [13, 14, 16, 20, 21], "pytorch": [13, 15, 16, 20, 21, 24], "everyth": 13, "brand": 13, "safer": 13, "mess": 13, "incompat": 13, "subdirectori": 13, "pip": 13, "verifi": 13, "uninstal": 13, "create_conda_env_rlgpu": 13, "sh": 13, "rlgpu": [13, 19], "wish": [13, 14, 20, 21, 23], "edit": [13, 19, 20], "rlgpu_conda_env": 13, "yml": 13, "env_nam": 13, "variabl": [13, 21, 23], "script": [13, 20, 23, 24], "remov": [13, 16, 19, 20, 23], "experiment": [13, 20], "instruct": 13, "toolkit": 13, "bash": 13, "build": [13, 23], "insid": [13, 23], "xdpyinfo": 13, "visit": 13, "readm": [13, 24], "simpli": [13, 20, 21], "clone": [13, 21], "repositori": 13, "joint_monkei": [13, 14, 19], "assum": [13, 20, 21], "monkei": 13, "launch": [13, 20], "good": [13, 19, 21, 23], "headless": [13, 20, 24], "break": 13, "occasion": [13, 24], "crash": [13, 21, 24], "problem": [13, 17], "sure": [13, 20, 21, 23], "meet": 13, "On": 13, "intel": 13, "sudo": 13, "prime": 13, "vulkan": 13, "vulkaninfo": 13, "explicitli": [13, 14, 24], "vk_icd_filenam": 13, "usr": 13, "icd": 13, "nvidia_icd": 13, "json": 13, "importerror": 13, "libpython3": 13, "7m": 13, "open": 13, "lib": 13, "apt": 13, "ld_library_path": 13, "appropri": [13, 21], "home": [13, 14], "xyz": 13, "anaconda3": 13, "bin": 13, "libstdc": 13, "glibcxx_3": 13, "carbgym": 13, "_bind": 13, "linux64": 13, "gym_36": 13, "distribut": [13, 24], "shenanigan": 13, "built": 13, "pretti": [13, 23], "simplest": 13, "hide": 13, "conflict": 13, "mkdir": 13, "your_conda_env": 13, "_unus": 13, "mv": 13, "gymasset": [14, 19, 20], "definiton": [14, 20], "necessari": [14, 16, 19, 20], "sometim": [14, 20], "extern": [14, 15, 20, 21], "within": [14, 16, 19, 20, 23], "tree": [14, 20], "absolut": [14, 20], "asset_root": [14, 20], "asset_fil": [14, 20], "franka_descript": [14, 20], "franka_panda": [14, 20], "xml": [14, 20], "extra": [14, 19, 20, 21], "accomplish": [14, 20, 21], "asset_opt": [14, 20, 24], "assimp": [14, 24], "prioriti": 14, "tri": 14, "incomplet": 14, "smooth": [14, 24], "seper": 14, "incorrect": [14, 19, 24], "wild": 14, "seemingli": 14, "arbitrari": [14, 23], "caus": [14, 23, 24], "artifact": 14, "overcom": 14, "convex_decomposit": 14, "abl": [14, 20], "effici": [14, 19, 20, 21], "accur": [14, 21, 23, 24], "consist": [14, 20, 21], "particularli": [14, 21], "applic": [14, 16, 20, 21], "grasp": [14, 23], "parti": 14, "librari": [14, 20, 21], "v": [14, 16, 23, 24], "hacd": 14, "affect": [14, 16, 19, 20, 21, 23, 24], "qualiti": [14, 23], "expos": [14, 16, 21, 24], "300000": 14, "paramat": 14, "simpl": [14, 16, 20, 21, 22, 24], "strategi": 14, "decompos": 14, "subsequ": [14, 20, 21], "safe": 14, "click": 14, "tab": [14, 20], "gui": 14, "checkbox": 14, "geometr": 14, "box_asset": 14, "sphere_asset": 14, "capsule_asset": 14, "inspect": 14, "asset_info": 14, "serv": [14, 20], "blueprint": [14, 20], "loader": 14, "contyp": 14, "conaffin": 14, "progress": 14, "stl": [14, 24], "dae": [14, 24], "primit": [14, 23, 24], "humanoid_20_5": 14, "shoulder": 14, "hip": 14, "compound": 14, "worldbodi": 14, "child": 14, "measur": [15, 20], "experienc": [15, 16, 21], "due": [15, 19, 22, 23], "bodynam": 15, "sensor_pose1": 15, "sensor_pose2": 15, "sensor_idx1": 15, "sensor_idx2": 15, "sensor_prop": 15, "sensor_idx": 15, "sensor_pos": 15, "actor_handl": [15, 16, 19, 20, 21], "num_sensor": 15, "sensor_data": 15, "get_forc": 15, "descriptor": [15, 21], "wrap": [15, 21], "discuss": [15, 20, 21], "document": [15, 24], "_fsdata": 15, "fsdata": 15, "gymtorch": [15, 16, 21], "wrap_tensor": [15, 16, 21], "These": [15, 20, 21, 23], "although": [15, 20], "quit": [15, 19, 20, 21, 23], "act": 15, "_forc": 15, "dimension": [15, 21], "aspect": [16, 20, 21], "programatt": 16, "addition": 16, "modif": 16, "topic": [16, 20], "gymcameraproperti": 16, "camera_prop": 16, "75": [16, 23], "1920": 16, "1080": 16, "supersampl": 16, "wide": [16, 19], "resampl": 16, "tall": 16, "meant": [16, 20, 21], "easili": [16, 20, 21], "128": 16, "camera_handl": 16, "creation": [16, 19], "tx": 16, "ty": 16, "yz": 16, "tz": 16, "environemnt": 16, "np": [16, 19], "45": 16, "track": 16, "local_transform": 16, "body_handl": 16, "readi": 16, "selector": 16, "4x": 16, "unsign": 16, "rgba": 16, "neg": [16, 23], "32bit": 16, "semant": 16, "2x": 16, "16bit": 16, "sign": 16, "optical_flow_imag": 16, "optical_flow_in_pixel": 16, "u": 16, "image_width": 16, "15": [16, 21], "image_height": 16, "color_imag": 16, "tightli": 16, "pack": [16, 20], "fastest": 16, "alreadi": 16, "optim": [16, 19], "prevent": [16, 20, 21, 23], "framework": [16, 20, 21, 24], "better": 16, "cam_handl": 16, "phase": 16, "camera_tensor": 16, "deep": [16, 20, 23], "torch_camera_tensor": 16, "now": [16, 20, 21, 24], "main": [16, 21, 24], "declar": 16, "stop": 16, "hazard": 16, "digest": [16, 21], "stall": 16, "written": [16, 20], "minim": [16, 20, 21], "were": [16, 21, 24], "convent": [16, 20], "deadlock": 16, "chapter": [16, 20], "design": [16, 20, 21, 24], "programmat": 16, "attribut": 16, "techniqu": 16, "briefli": 16, "touch": 16, "routin": 16, "rigid_body_index": 16, "4th": 16, "caller": 16, "current_color": 16, "texture_handl": 16, "texture_filenam": 16, "bmp": 16, "pixelarrai": 16, "uint8_t": 16, "strongli": 16, "recommend": [16, 21, 23], "fashion": 16, "assign": [16, 20], "spheric": [16, 19, 24], "freed": 16, "gymsim": 16, "multip": 16, "guarante": 16, "tint": 16, "accord": 16, "differenti": 16, "programat": 16, "light_index": 16, "throuhg": 16, "classic": [16, 21], "phong": 16, "give": [16, 20, 23], "ise": 16, "univers": [16, 23], "debug": [16, 20, 21, 23], "quantiti": 16, "rudimentari": 16, "accumul": 16, "anoth": [16, 20, 21, 23], "flush": 16, "num_lin": 16, "3d": [16, 21], "clearn": 16, "afterward": 16, "generate_lin": 16, "line_vertic": 16, "line_color": 16, "drawn": 16, "deproject": 16, "cloud": 16, "knowledg": 16, "term": [16, 23], "aid": 16, "projection_matrix": 16, "view_matrix": 16, "commun": [16, 20], "case": [16, 20, 21, 23], "nth": 16, "boundari": 16, "explicit": 16, "cleanup": 17, "procedur": [17, 20, 24], "relev": 17, "intrins": 17, "accompani": 19, "someth": [19, 21], "env_low": [19, 20], "env_upp": [19, 20], "num_per_row": 19, "append": [19, 20], "commonli": 19, "get_actor_": 19, "set_actor_": 19, "apply_actor_": 19, "tell": 19, "cluster": [19, 20], "modest": 19, "boost": 19, "enclos": 19, "max_bodi": 19, "max_shap": 19, "num_bodi": 19, "num_joint": 19, "constist": 19, "shown": [19, 20], "body_physics_prop": [19, 24], "preserv": 19, "plan": [19, 21], "actor_sc": 19, "yield": [19, 21], "expect": 19, "resolv": [19, 23], "adjust": 19, "getter": [19, 24], "haslimit": 19, "unlimit": 19, "drivemod": [19, 21], "freeli": 19, "fill": [19, 21], "projectil": [19, 20], "ant": 19, "essenti": [19, 21], "ragdol": 19, "cumul": 19, "begin": [19, 20, 21], "configur": [19, 20, 21, 23, 24], "astyp": 19, "engag": 19, "pd": [19, 21], "proport": 19, "poserror": 19, "velerror": 19, "1000": 19, "200": 19, "f": 19, "dof_prop": 19, "lower_limit": 19, "upper_limit": 19, "pos_target": 19, "600": 19, "vel_target": 19, "uniform": [19, 20], "pi": [19, 20], "dof_control": 19, "flexibl": 19, "overhead": [19, 20, 21], "maxim": 19, "body_st": 19, "nontrivi": 19, "unnecessari": 19, "restor": 19, "i1": 19, "body_nam": 19, "i2": 19, "i3": 19, "reduc": [19, 22, 23], "dof_stat": [19, 21], "intellig": 20, "facilit": [20, 21], "exchang": 20, "client": 20, "hierarch": 20, "flat": [20, 22], "manipul": [20, 21, 23], "multidimension": 20, "singleton": 20, "acquir": [20, 21], "startup": [20, 24], "acquire_gym": 20, "itself": [20, 21], "proxi": 20, "sim_param": [20, 21], "compute_device_id": [20, 21], "context": [20, 24], "multi": [20, 21, 24], "role": [20, 21], "choic": [20, 23], "offer": 20, "characterist": 20, "pick": [20, 21], "foll": 20, "snippet": [20, 21, 23], "9": [20, 21], "physics_engin": 20, "legaci": 20, "hard": 20, "special": [20, 21], "attent": 20, "unless": 20, "plane_param": 20, "align": [20, 24], "tilt": 20, "elast": [20, 24], "thu": [20, 21], "snapshot": 20, "franka_cube_ik": 20, "tabl": 20, "monitor": 20, "Their": 20, "area": 20, "With": [20, 21, 24], "ten": 20, "lockstep": 20, "condit": [20, 21], "layout": [20, 21], "themselv": 20, "advantag": 20, "overal": [20, 23], "humanoid": [20, 24], "walk": [20, 21], "item": 20, "cupboard": 20, "involv": 20, "meaning": 20, "compar": 20, "gather": [20, 21], "own": [20, 21], "embed": [20, 24], "extent": 20, "arrang": 20, "sourc": [20, 21, 23], "707107": 20, "myactor": 20, "constructor": 20, "90": 20, "Such": 20, "conveni": [20, 21], "angl": 20, "enforc": 20, "identifi": 20, "cost": 20, "collision_group": 20, "collision_filt": 20, "plai": 20, "fine": [20, 24], "grain": 20, "collison": 20, "kind": [20, 23], "envs_per_row": 20, "sequenc": 20, "finish": 20, "batch": 20, "restrict": 20, "lift": 20, "impli": 20, "fake": 20, "cam_prop": 20, "pop": [20, 23], "execut": 20, "separ": [20, 23], "absenc": 20, "refresh": [20, 21, 24], "quickli": [20, 23], "frequenc": [20, 23, 24], "statement": 20, "down": 20, "slower": [20, 24], "incorpor": 20, "fullscreen": 20, "toggl": [20, 24], "f11": 20, "perf": 20, "sub": [20, 23], "put": [20, 21], "slider": 20, "noteworthi": 20, "averag": [20, 24], "fp": 20, "space_shoot": 20, "mouse_shoot": 20, "evt": 20, "exit": 20, "compat": 21, "forth": 21, "similarli": 21, "approach": 21, "benefit": [21, 23], "elimin": 21, "consider": 21, "especi": [21, 23, 24], "establish": 21, "popular": 21, "tensorflow": 21, "desciptor": 21, "usabl": 21, "exist": [21, 24], "utilti": 21, "content": [21, 24], "abstract": 21, "furthermor": 21, "implicit": 21, "_root_tensor": 21, "isn": 21, "why": 21, "prefix": 21, "underscor": 21, "root_tensor": 21, "re": 21, "invent": 21, "wheel": 21, "standard": 21, "root_posit": 21, "root_orient": 21, "root_linvel": 21, "root_angvel": 21, "ll": 21, "interfer": 21, "contriv": 21, "rais": 21, "torch": 21, "repeat": 21, "notic": 21, "upward": 21, "regardless": 21, "period": 21, "saved_root_tensor": 21, "unwrap_tensor": 21, "subset": 21, "actor_indic": 21, "42": 21, "int32": 21, "_root_stat": 21, "word": 21, "easier": 21, "laid": 21, "ith": 21, "dof_nam": 21, "_dof_stat": 21, "actor_index_tensor": 21, "unaffect": 21, "entri": 21, "_actor_indic": 21, "rb_name": 21, "_rb_state": 21, "rb_state": 21, "cartesian": 21, "_jacobian": 21, "_massmatrix": 21, "mm": 21, "num_link": 21, "11": 21, "six": 21, "particular": 21, "env_index": 21, "link_index": 21, "axis_index": 21, "dof_index": 21, "panda_joint5": 21, "link_dict": 21, "franka_asset": 21, "dof_dict": 21, "Then": 21, "movabl": 21, "absent": 21, "ik": 21, "osc": 21, "express": 21, "_net_cf": 21, "net_cf": 21, "balanc": 21, "equival": [21, 23], "slowest": 21, "situat": [21, 23], "stabl": [21, 23], "tend": 21, "persist": 21, "ensur": [21, 23], "restart": 21, "sparingli": 21, "account": 21, "rand": 21, "variant": [21, 24], "set_dof_velocity_tensor_index": 21, "force_tensor": 21, "torque_tensor": 21, "pos_tensor": 21, "apply_forc": 21, "apply_forces_at_po": 21, "garbag": 21, "subject": 21, "interpret": 21, "particip": 21, "still": [21, 24], "tricki": 21, "difficult": 21, "longtensor": 21, "consid": 21, "indices_torch": 21, "indices_gym": 21, "subtl": 21, "record": 21, "temporari": 21, "undefin": 21, "corrupt": 21, "solut": 21, "whenev": [21, 23], "rewrit": 21, "indices_torch32": 21, "aliv": 21, "gracefulli": 21, "mind": 21, "setter": 21, "root_stat": 21, "indices1": 21, "n1": 21, "indices2": 21, "n2": 21, "combined_indic": 21, "n_total": 21, "union": 21, "stale": 21, "uneven": 22, "terrain_util": 22, "terrain_cr": 22, "showcas": 22, "anymalterrain": 22, "unreli": 22, "edg": [22, 23], "miss": [22, 23, 24], "workaround": 22, "propos": 22, "soon": 23, "fairli": 23, "50th": 23, "strong": 23, "assembl": 23, "subdivid": 23, "interv": 23, "achiev": 23, "But": 23, "120": 23, "descent": 23, "moder": 23, "nonlinear": 23, "choleski": 23, "eigen": 23, "pcg1": 23, "precondit": 23, "conjug": 23, "gradient": 23, "pcg2": 23, "residu": 23, "slow": 23, "mostli": 23, "verif": 23, "outer": 23, "suffici": 23, "numouteriter": 23, "conserv": 23, "bounci": 23, "behaviour": [23, 24], "presenc": 23, "movement": 23, "rapidli": 23, "lot": 23, "higher": 23, "warm": 23, "specul": 23, "margin": 23, "enough": 23, "formal": 23, "travel": 23, "drop": 23, "low": 23, "interpenetr": 23, "eject": 23, "decreas": 23, "simulation": 23, "costli": 23, "lack": 23, "bad": 23, "diagnos": 23, "shapecollisiondist": 23, "motor": 23, "weaker": 23, "bvh": 23, "thought": 23, "degener": 23, "unifi": 23, "polyhedra": 23, "soup": 23, "region": 23, "layer": 23, "tempor": 23, "02": 23, "energi": 23, "gain": 23, "rule": 23, "inject": 23, "pxrigidbodi": 23, "setmaxdepenetrationveloc": 23, "100m": 23, "big": 23, "bigger": 23, "influenc": 23, "cheap": 23, "cheaper": 23, "highli": 23, "inflat": 23, "trigger": 23, "natur": 23, "anchor": 23, "025": 23, "tutori": 23, "premake5": 23, "lua": 23, "physxlib": 23, "profil": 23, "rebuild": 23, "localhost": 23, "machin": 23, "gym_pvd_host": 23, "ip": 23, "hostnam": 23, "os": 23, "xxx": 23, "yyi": 23, "zzz": 23, "www": 23, "live": 23, "gym_pvd_fil": 23, "omit": 23, "foo": 23, "pxd2": 23, "preview": 24, "2022": 24, "simplifi": 24, "workload": 24, "sdf": 24, "nut": 24, "bolt": 24, "pars": 24, "incorrectli": 24, "overriden": 24, "doc": 24, "blob": 24, "md": 24, "inclin": 24, "docker": 24, "deeplearn": 24, "rel_21": 24, "09": 24, "html": 24, "physx_gpu": 24, "experiment_nam": 24, "metadata": 24, "refactor": 24, "seed": 24, "torch_determinist": 24, "reproduc": 24, "inherit": 24, "cfg": 24, "opt": 24, "workspac": 24, "shadow": 24, "kl": 24, "schedul": 24, "rl_game": 24, "closer": 24, "openai": 24, "dexter": 24, "blog": 24, "asymmetr": 24, "lstm": 24, "polici": 24, "controlfrequencyinv": 24, "yaml": 24, "config": 24, "nois": 24, "anym": 24, "quadrup": 24, "quadcopt": 24, "nasa": 24, "ingenu": 24, "helicopt": 24, "scalabl": 24, "container": 24, "cmu": 24, "nv": 24, "ellipsoid": 24, "submit": 24, "mix": 24, "lookat": 24, "drag": 24, "erron": 24, "overflow": 24, "pvd": 24, "remot": 24, "log": 24, "sync": 24, "multiprocess": 24, "supersed": 24, "old": 24, "rlbase": 24, "around": 24, "rtx": 24, "renam": 24}, "objects": {"isaacgym.gymapi": [[2, 0, 1, "", "AXIS_ALL"], [2, 0, 1, "", "AXIS_NONE"], [2, 0, 1, "", "AXIS_ROTATION"], [2, 0, 1, "", "AXIS_SWING_1"], [2, 0, 1, "", "AXIS_SWING_2"], [2, 0, 1, "", "AXIS_TRANSLATION"], [2, 0, 1, "", "AXIS_TWIST"], [2, 0, 1, "", "AXIS_X"], [2, 0, 1, "", "AXIS_Y"], [2, 0, 1, "", "AXIS_Z"], [6, 1, 1, "", "ActionEvent"], [6, 1, 1, "", "AssetOptions"], [6, 1, 1, "", "AttractorProperties"], [3, 1, 1, "", "CameraFollowMode"], [6, 1, 1, "", "CameraProperties"], [3, 1, 1, "", "ContactCollection"], [3, 1, 1, "", "CoordinateSpace"], [2, 0, 1, "", "DEFAULT_VIEWER_HEIGHT"], [2, 0, 1, "", "DEFAULT_VIEWER_WIDTH"], [3, 1, 1, "", "DofDriveMode"], [6, 1, 1, "", "DofFrame"], [6, 1, 1, "", "DofState"], [3, 1, 1, "", "DofType"], [6, 1, 1, "", "FlexParams"], [6, 1, 1, "", "ForceSensorProperties"], [4, 1, 1, "", "Gym"], [6, 1, 1, "", "HeightFieldParams"], [2, 0, 1, "", "INVALID_HANDLE"], [3, 1, 1, "", "ImageType"], [3, 1, 1, "", "IndexDomain"], [6, 1, 1, "", "IndexRange"], [3, 1, 1, "", "JointType"], [3, 1, 1, "", "KeyboardInput"], [6, 1, 1, "", "Mat33"], [6, 1, 1, "", "Mat44"], [3, 1, 1, "", "MeshType"], [3, 1, 1, "", "MouseInput"], [6, 1, 1, "", "PerformanceTimers"], [6, 1, 1, "", "PhysXParams"], [6, 1, 1, "", "PlaneParams"], [6, 1, 1, "", "Quat"], [2, 0, 1, "", "RIGID_BODY_DISABLE_GRAVITY"], [2, 0, 1, "", "RIGID_BODY_DISABLE_SIMULATION"], [2, 0, 1, "", "RIGID_BODY_NONE"], [6, 1, 1, "", "RigidBodyProperties"], [6, 1, 1, "", "RigidBodyState"], [6, 1, 1, "", "RigidContact"], [6, 1, 1, "", "RigidShapeProperties"], [2, 0, 1, "", "STATE_ALL"], [2, 0, 1, "", "STATE_NONE"], [2, 0, 1, "", "STATE_POS"], [2, 0, 1, "", "STATE_VEL"], [6, 1, 1, "", "SimParams"], [3, 1, 1, "", "SimType"], [6, 1, 1, "", "SoftMaterial"], [3, 1, 1, "", "SoftMaterialType"], [3, 1, 1, "", "TendonType"], [6, 1, 1, "", "Tensor"], [3, 1, 1, "", "TensorDataType"], [6, 1, 1, "", "Transform"], [6, 1, 1, "", "TriangleMeshParams"], [3, 1, 1, "", "UpAxis"], [6, 1, 1, "", "Vec3"], [6, 1, 1, "", "Velocity"], [6, 1, 1, "", "Version"], [6, 1, 1, "", "VhacdParams"]], "isaacgym.gymapi.ActionEvent": [[6, 2, 1, "", "action"], [6, 2, 1, "", "value"]], "isaacgym.gymapi.AssetOptions": [[6, 2, 1, "", "angular_damping"], [6, 2, 1, "", "armature"], [6, 2, 1, "", "collapse_fixed_joints"], [6, 2, 1, "", "convex_decomposition_from_submeshes"], [6, 2, 1, "", "default_dof_drive_mode"], [6, 2, 1, "", "density"], [6, 2, 1, "", "disable_gravity"], [6, 2, 1, "", "enable_gyroscopic_forces"], [6, 2, 1, "", "fix_base_link"], [6, 2, 1, "", "flip_visual_attachments"], [6, 2, 1, "", "linear_damping"], [6, 2, 1, "", "max_angular_velocity"], [6, 2, 1, "", "max_linear_velocity"], [6, 2, 1, "", "mesh_normal_mode"], [6, 2, 1, "", "min_particle_mass"], [6, 2, 1, "", "override_com"], [6, 2, 1, "", "override_inertia"], [6, 2, 1, "", "replace_cylinder_with_capsule"], [6, 2, 1, "", "slices_per_cylinder"], [6, 2, 1, "", "tendon_limit_stiffness"], [6, 2, 1, "", "thickness"], [6, 2, 1, "", "use_mesh_materials"], [6, 2, 1, "", "use_physx_armature"], [6, 2, 1, "", "vhacd_enabled"], [6, 2, 1, "", "vhacd_params"]], "isaacgym.gymapi.AttractorProperties": [[6, 2, 1, "", "axes"], [6, 2, 1, "", "damping"], [6, 2, 1, "", "offset"], [6, 2, 1, "", "rigid_handle"], [6, 2, 1, "", "stiffness"], [6, 2, 1, "", "target"]], "isaacgym.gymapi.CameraProperties": [[6, 2, 1, "", "enable_tensors"], [6, 2, 1, "", "far_plane"], [6, 2, 1, "", "height"], [6, 2, 1, "", "horizontal_fov"], [6, 2, 1, "", "near_plane"], [6, 2, 1, "", "supersampling_horizontal"], [6, 2, 1, "", "supersampling_vertical"], [6, 2, 1, "", "use_collision_geometry"], [6, 2, 1, "", "width"]], "isaacgym.gymapi.DofFrame": [[6, 2, 1, "", "axis"], [6, 0, 1, "", "dtype"], [6, 3, 1, "", "from_buffer"], [6, 2, 1, "", "origin"]], "isaacgym.gymapi.DofState": [[6, 0, 1, "", "dtype"], [6, 2, 1, "", "pos"], [6, 2, 1, "", "vel"]], "isaacgym.gymapi.FlexParams": [[6, 2, 1, "", "contact_regularization"], [6, 2, 1, "", "deterministic_mode"], [6, 2, 1, "", "dynamic_friction"], [6, 2, 1, "", "friction_mode"], [6, 2, 1, "", "geometric_stiffness"], [6, 2, 1, "", "max_rigid_contacts"], [6, 2, 1, "", "max_soft_contacts"], [6, 2, 1, "", "num_inner_iterations"], [6, 2, 1, "", "num_outer_iterations"], [6, 2, 1, "", "particle_friction"], [6, 2, 1, "", "relaxation"], [6, 2, 1, "", "return_contacts"], [6, 2, 1, "", "shape_collision_distance"], [6, 2, 1, "", "shape_collision_margin"], [6, 2, 1, "", "solver_type"], [6, 2, 1, "", "static_friction"], [6, 2, 1, "", "warm_start"]], "isaacgym.gymapi.ForceSensorProperties": [[6, 2, 1, "", "enable_constraint_solver_forces"], [6, 2, 1, "", "enable_forward_dynamics_forces"], [6, 2, 1, "", "use_world_frame"]], "isaacgym.gymapi.Gym": [[4, 3, 1, "", "acquire_actor_root_state_tensor"], [4, 3, 1, "", "acquire_dof_force_tensor"], [4, 3, 1, "", "acquire_dof_state_tensor"], [4, 3, 1, "", "acquire_force_sensor_tensor"], [4, 3, 1, "", "acquire_jacobian_tensor"], [4, 3, 1, "", "acquire_mass_matrix_tensor"], [4, 3, 1, "", "acquire_net_contact_force_tensor"], [4, 3, 1, "", "acquire_particle_state_tensor"], [4, 3, 1, "", "acquire_pneumatic_pressure_tensor"], [4, 3, 1, "", "acquire_pneumatic_target_tensor"], [4, 3, 1, "", "acquire_rigid_body_state_tensor"], [4, 3, 1, "", "add_ground"], [4, 3, 1, "", "add_heightfield"], [4, 3, 1, "", "add_lines"], [4, 3, 1, "", "add_triangle_mesh"], [4, 3, 1, "", "apply_actor_dof_efforts"], [4, 3, 1, "", "apply_body_force_at_pos"], [4, 3, 1, "", "apply_body_forces"], [4, 3, 1, "", "apply_dof_effort"], [4, 3, 1, "", "apply_rigid_body_force_at_pos_tensors"], [4, 3, 1, "", "apply_rigid_body_force_tensors"], [4, 3, 1, "", "attach_camera_to_body"], [4, 3, 1, "", "attach_sim"], [4, 3, 1, "", "begin_aggregate"], [4, 3, 1, "", "clear_lines"], [4, 3, 1, "", "create_actor"], [4, 3, 1, "", "create_aggregate"], [4, 3, 1, "", "create_asset_force_sensor"], [4, 3, 1, "", "create_box"], [4, 3, 1, "", "create_camera_sensor"], [4, 3, 1, "", "create_capsule"], [4, 3, 1, "", "create_cloth_grid"], [4, 3, 1, "", "create_env"], [4, 3, 1, "", "create_performance_timers"], [4, 3, 1, "", "create_rigid_body_attractor"], [4, 3, 1, "", "create_sim"], [4, 3, 1, "", "create_sphere"], [4, 3, 1, "", "create_tet_grid"], [4, 3, 1, "", "create_texture_from_buffer"], [4, 3, 1, "", "create_texture_from_file"], [4, 3, 1, "", "create_usd_exporter"], [4, 3, 1, "", "create_viewer"], [4, 3, 1, "", "debug_print_asset"], [4, 3, 1, "", "destroy_camera_sensor"], [4, 3, 1, "", "destroy_env"], [4, 3, 1, "", "destroy_performance_timers"], [4, 3, 1, "", "destroy_sim"], [4, 3, 1, "", "destroy_usd_exporter"], [4, 3, 1, "", "destroy_viewer"], [4, 3, 1, "", "draw_env_rigid_contacts"], [4, 3, 1, "", "draw_env_soft_contacts"], [4, 3, 1, "", "draw_viewer"], [4, 3, 1, "", "enable_actor_dof_force_sensors"], [4, 3, 1, "", "end_access_image_tensors"], [4, 3, 1, "", "end_aggregate"], [4, 3, 1, "", "export_usd_asset"], [4, 3, 1, "", "export_usd_sim"], [4, 3, 1, "", "fetch_results"], [4, 3, 1, "", "find_actor_actuator_index"], [4, 3, 1, "", "find_actor_dof_handle"], [4, 3, 1, "", "find_actor_dof_index"], [4, 3, 1, "", "find_actor_fixed_tendon_joint_index"], [4, 3, 1, "", "find_actor_handle"], [4, 3, 1, "", "find_actor_index"], [4, 3, 1, "", "find_actor_joint_handle"], [4, 3, 1, "", "find_actor_joint_index"], [4, 3, 1, "", "find_actor_rigid_body_handle"], [4, 3, 1, "", "find_actor_rigid_body_index"], [4, 3, 1, "", "find_actor_tendon_index"], [4, 3, 1, "", "find_asset_actuator_index"], [4, 3, 1, "", "find_asset_dof_index"], [4, 3, 1, "", "find_asset_joint_index"], [4, 3, 1, "", "find_asset_rigid_body_index"], [4, 3, 1, "", "find_asset_tendon_index"], [4, 3, 1, "", "free_texture"], [4, 3, 1, "", "get_actor_actuator_count"], [4, 3, 1, "", "get_actor_actuator_joint_name"], [4, 3, 1, "", "get_actor_actuator_name"], [4, 3, 1, "", "get_actor_actuator_properties"], [4, 3, 1, "", "get_actor_asset"], [4, 3, 1, "", "get_actor_count"], [4, 3, 1, "", "get_actor_dof_count"], [4, 3, 1, "", "get_actor_dof_dict"], [4, 3, 1, "", "get_actor_dof_forces"], [4, 3, 1, "", "get_actor_dof_frames"], [4, 3, 1, "", "get_actor_dof_handle"], [4, 3, 1, "", "get_actor_dof_index"], [4, 3, 1, "", "get_actor_dof_names"], [4, 3, 1, "", "get_actor_dof_position_targets"], [4, 3, 1, "", "get_actor_dof_properties"], [4, 3, 1, "", "get_actor_dof_states"], [4, 3, 1, "", "get_actor_dof_velocity_targets"], [4, 3, 1, "", "get_actor_fixed_tendon_joint_coefficients"], [4, 3, 1, "", "get_actor_fixed_tendon_joint_name"], [4, 3, 1, "", "get_actor_force_sensor"], [4, 3, 1, "", "get_actor_force_sensor_count"], [4, 3, 1, "", "get_actor_handle"], [4, 3, 1, "", "get_actor_index"], [4, 3, 1, "", "get_actor_joint_count"], [4, 3, 1, "", "get_actor_joint_dict"], [4, 3, 1, "", "get_actor_joint_handle"], [4, 3, 1, "", "get_actor_joint_index"], [4, 3, 1, "", "get_actor_joint_names"], [4, 3, 1, "", "get_actor_joint_transforms"], [4, 3, 1, "", "get_actor_name"], [4, 3, 1, "", "get_actor_rigid_body_count"], [4, 3, 1, "", "get_actor_rigid_body_dict"], [4, 3, 1, "", "get_actor_rigid_body_handle"], [4, 3, 1, "", "get_actor_rigid_body_index"], [4, 3, 1, "", "get_actor_rigid_body_names"], [4, 3, 1, "", "get_actor_rigid_body_properties"], [4, 3, 1, "", "get_actor_rigid_body_shape_indices"], [4, 3, 1, "", "get_actor_rigid_body_states"], [4, 3, 1, "", "get_actor_rigid_shape_count"], [4, 3, 1, "", "get_actor_rigid_shape_properties"], [4, 3, 1, "", "get_actor_root_rigid_body_handle"], [4, 3, 1, "", "get_actor_scale"], [4, 3, 1, "", "get_actor_soft_body_count"], [4, 3, 1, "", "get_actor_soft_materials"], [4, 3, 1, "", "get_actor_tendon_count"], [4, 3, 1, "", "get_actor_tendon_name"], [4, 3, 1, "", "get_actor_tendon_offset"], [4, 3, 1, "", "get_actor_tendon_properties"], [4, 3, 1, "", "get_actor_tetrahedra_range"], [4, 3, 1, "", "get_actor_triangle_range"], [4, 3, 1, "", "get_asset_actuator_count"], [4, 3, 1, "", "get_asset_actuator_joint_name"], [4, 3, 1, "", "get_asset_actuator_name"], [4, 3, 1, "", "get_asset_actuator_properties"], [4, 3, 1, "", "get_asset_dof_count"], [4, 3, 1, "", "get_asset_dof_dict"], [4, 3, 1, "", "get_asset_dof_name"], [4, 3, 1, "", "get_asset_dof_names"], [4, 3, 1, "", "get_asset_dof_properties"], [4, 3, 1, "", "get_asset_dof_type"], [4, 3, 1, "", "get_asset_fixed_tendon_joint_coefficients"], [4, 3, 1, "", "get_asset_fixed_tendon_joint_name"], [4, 3, 1, "", "get_asset_joint_count"], [4, 3, 1, "", "get_asset_joint_dict"], [4, 3, 1, "", "get_asset_joint_name"], [4, 3, 1, "", "get_asset_joint_names"], [4, 3, 1, "", "get_asset_joint_type"], [4, 3, 1, "", "get_asset_rigid_body_count"], [4, 3, 1, "", "get_asset_rigid_body_dict"], [4, 3, 1, "", "get_asset_rigid_body_name"], [4, 3, 1, "", "get_asset_rigid_body_names"], [4, 3, 1, "", "get_asset_rigid_body_shape_indices"], [4, 3, 1, "", "get_asset_rigid_shape_count"], [4, 3, 1, "", "get_asset_rigid_shape_properties"], [4, 3, 1, "", "get_asset_soft_body_count"], [4, 3, 1, "", "get_asset_soft_materials"], [4, 3, 1, "", "get_asset_tendon_count"], [4, 3, 1, "", "get_asset_tendon_name"], [4, 3, 1, "", "get_asset_tendon_properties"], [4, 3, 1, "", "get_attractor_properties"], [4, 3, 1, "", "get_camera_image"], [4, 3, 1, "", "get_camera_image_gpu_tensor"], [4, 3, 1, "", "get_camera_proj_matrix"], [4, 3, 1, "", "get_camera_transform"], [4, 3, 1, "", "get_camera_view_matrix"], [4, 3, 1, "", "get_dof_frame"], [4, 3, 1, "", "get_dof_position"], [4, 3, 1, "", "get_dof_target_position"], [4, 3, 1, "", "get_dof_target_velocity"], [4, 3, 1, "", "get_dof_type_string"], [4, 3, 1, "", "get_dof_velocity"], [4, 3, 1, "", "get_elapsed_time"], [4, 3, 1, "", "get_env"], [4, 3, 1, "", "get_env_count"], [4, 3, 1, "", "get_env_dof_count"], [4, 3, 1, "", "get_env_joint_count"], [4, 3, 1, "", "get_env_origin"], [4, 3, 1, "", "get_env_rigid_body_count"], [4, 3, 1, "", "get_env_rigid_body_states"], [4, 3, 1, "", "get_env_rigid_contact_forces"], [4, 3, 1, "", "get_env_rigid_contacts"], [4, 3, 1, "", "get_frame_count"], [4, 3, 1, "", "get_joint_handle"], [4, 3, 1, "", "get_joint_name"], [4, 3, 1, "", "get_joint_position"], [4, 3, 1, "", "get_joint_target_position"], [4, 3, 1, "", "get_joint_target_velocity"], [4, 3, 1, "", "get_joint_transform"], [4, 3, 1, "", "get_joint_type_string"], [4, 3, 1, "", "get_joint_velocity"], [4, 3, 1, "", "get_performance_timers"], [4, 3, 1, "", "get_pneumatic_pressure"], [4, 3, 1, "", "get_pneumatic_target"], [4, 3, 1, "", "get_rigid_angular_velocity"], [4, 3, 1, "", "get_rigid_body_color"], [4, 3, 1, "", "get_rigid_body_segmentation_id"], [4, 3, 1, "", "get_rigid_body_texture"], [4, 3, 1, "", "get_rigid_contact_forces"], [4, 3, 1, "", "get_rigid_contacts"], [4, 3, 1, "", "get_rigid_handle"], [4, 3, 1, "", "get_rigid_linear_velocity"], [4, 3, 1, "", "get_rigid_name"], [4, 3, 1, "", "get_rigid_transform"], [4, 3, 1, "", "get_sensor"], [4, 3, 1, "", "get_sim_actor_count"], [4, 3, 1, "", "get_sim_dof_count"], [4, 3, 1, "", "get_sim_force_sensor_count"], [4, 3, 1, "", "get_sim_joint_count"], [4, 3, 1, "", "get_sim_params"], [4, 3, 1, "", "get_sim_rigid_body_count"], [4, 3, 1, "", "get_sim_rigid_body_states"], [4, 3, 1, "", "get_sim_tetrahedra"], [4, 3, 1, "", "get_sim_tetrahedra_count"], [4, 3, 1, "", "get_sim_time"], [4, 3, 1, "", "get_sim_triangle_count"], [4, 3, 1, "", "get_sim_triangles"], [4, 3, 1, "", "get_soft_contacts"], [4, 3, 1, "", "get_usd_export_root"], [4, 3, 1, "", "get_vec_actor_dof_states"], [4, 3, 1, "", "get_vec_env_rigid_contact_forces"], [4, 3, 1, "", "get_vec_rigid_angular_velocity"], [4, 3, 1, "", "get_vec_rigid_linear_velocity"], [4, 3, 1, "", "get_vec_rigid_transform"], [4, 3, 1, "", "get_version"], [4, 3, 1, "", "get_viewer_camera_handle"], [4, 3, 1, "", "get_viewer_camera_transform"], [4, 3, 1, "", "get_viewer_mouse_position"], [4, 3, 1, "", "get_viewer_size"], [4, 3, 1, "", "load_asset"], [4, 3, 1, "", "load_mjcf"], [4, 3, 1, "", "load_opensim"], [4, 3, 1, "", "load_sim"], [4, 3, 1, "", "load_urdf"], [4, 3, 1, "", "load_usd"], [4, 3, 1, "", "omni_connect"], [4, 3, 1, "", "omni_disconnect"], [4, 3, 1, "", "poll_viewer_events"], [4, 3, 1, "", "prepare_sim"], [4, 3, 1, "", "query_viewer_action_events"], [4, 3, 1, "", "query_viewer_has_closed"], [4, 3, 1, "", "refresh_actor_root_state_tensor"], [4, 3, 1, "", "refresh_dof_force_tensor"], [4, 3, 1, "", "refresh_dof_state_tensor"], [4, 3, 1, "", "refresh_force_sensor_tensor"], [4, 3, 1, "", "refresh_jacobian_tensors"], [4, 3, 1, "", "refresh_mass_matrix_tensors"], [4, 3, 1, "", "refresh_net_contact_force_tensor"], [4, 3, 1, "", "refresh_particle_state_tensor"], [4, 3, 1, "", "refresh_pneumatic_pressure_tensor"], [4, 3, 1, "", "refresh_pneumatic_target_tensor"], [4, 3, 1, "", "refresh_rigid_body_state_tensor"], [4, 3, 1, "", "render_all_camera_sensors"], [4, 3, 1, "", "reset_actor_materials"], [4, 3, 1, "", "reset_actor_particles_to_rest"], [4, 3, 1, "", "set_actor_dof_position_targets"], [4, 3, 1, "", "set_actor_dof_properties"], [4, 3, 1, "", "set_actor_dof_states"], [4, 3, 1, "", "set_actor_dof_velocity_targets"], [4, 3, 1, "", "set_actor_fixed_tendon_joint_coefficients"], [4, 3, 1, "", "set_actor_rigid_body_properties"], [4, 3, 1, "", "set_actor_rigid_body_states"], [4, 3, 1, "", "set_actor_rigid_shape_properties"], [4, 3, 1, "", "set_actor_root_state_tensor"], [4, 3, 1, "", "set_actor_root_state_tensor_indexed"], [4, 3, 1, "", "set_actor_scale"], [4, 3, 1, "", "set_actor_soft_materials"], [4, 3, 1, "", "set_actor_tendon_offset"], [4, 3, 1, "", "set_actor_tendon_properties"], [4, 3, 1, "", "set_asset_fixed_tendon_joint_coefficients"], [4, 3, 1, "", "set_asset_rigid_shape_properties"], [4, 3, 1, "", "set_asset_tendon_properties"], [4, 3, 1, "", "set_attractor_properties"], [4, 3, 1, "", "set_attractor_target"], [4, 3, 1, "", "set_camera_location"], [4, 3, 1, "", "set_camera_transform"], [4, 3, 1, "", "set_dof_actuation_force_tensor"], [4, 3, 1, "", "set_dof_actuation_force_tensor_indexed"], [4, 3, 1, "", "set_dof_position_target_tensor"], [4, 3, 1, "", "set_dof_position_target_tensor_indexed"], [4, 3, 1, "", "set_dof_state_tensor"], [4, 3, 1, "", "set_dof_state_tensor_indexed"], [4, 3, 1, "", "set_dof_target_position"], [4, 3, 1, "", "set_dof_target_velocity"], [4, 3, 1, "", "set_dof_velocity_target_tensor"], [4, 3, 1, "", "set_dof_velocity_target_tensor_indexed"], [4, 3, 1, "", "set_env_rigid_body_states"], [4, 3, 1, "", "set_joint_target_position"], [4, 3, 1, "", "set_joint_target_velocity"], [4, 3, 1, "", "set_light_parameters"], [4, 3, 1, "", "set_particle_state_tensor"], [4, 3, 1, "", "set_particle_state_tensor_indexed"], [4, 3, 1, "", "set_pneumatic_pressure"], [4, 3, 1, "", "set_pneumatic_pressure_tensor"], [4, 3, 1, "", "set_pneumatic_pressure_tensor_indexed"], [4, 3, 1, "", "set_pneumatic_target"], [4, 3, 1, "", "set_pneumatic_target_tensor"], [4, 3, 1, "", "set_pneumatic_target_tensor_indexed"], [4, 3, 1, "", "set_rigid_angular_velocity"], [4, 3, 1, "", "set_rigid_body_color"], [4, 3, 1, "", "set_rigid_body_segmentation_id"], [4, 3, 1, "", "set_rigid_body_state_tensor"], [4, 3, 1, "", "set_rigid_body_texture"], [4, 3, 1, "", "set_rigid_linear_velocity"], [4, 3, 1, "", "set_rigid_transform"], [4, 3, 1, "", "set_sim_device"], [4, 3, 1, "", "set_sim_params"], [4, 3, 1, "", "set_sim_rigid_body_states"], [4, 3, 1, "", "set_usd_export_root"], [4, 3, 1, "", "simulate"], [4, 3, 1, "", "start_access_image_tensors"], [4, 3, 1, "", "step_graphics"], [4, 3, 1, "", "subscribe_viewer_keyboard_event"], [4, 3, 1, "", "subscribe_viewer_mouse_event"], [4, 3, 1, "", "sync_frame_time"], [4, 3, 1, "", "viewer_camera_look_at"], [4, 3, 1, "", "write_camera_image_to_file"], [4, 3, 1, "", "write_viewer_image_to_file"]], "isaacgym.gymapi.HeightFieldParams": [[6, 2, 1, "", "column_scale"], [6, 2, 1, "", "dynamic_friction"], [6, 2, 1, "", "nbColumns"], [6, 2, 1, "", "nbRows"], [6, 2, 1, "", "restitution"], [6, 2, 1, "", "row_scale"], [6, 2, 1, "", "segmentation_id"], [6, 2, 1, "", "static_friction"], [6, 2, 1, "", "transform"], [6, 2, 1, "", "vertical_scale"]], "isaacgym.gymapi.IndexRange": [[6, 2, 1, "", "count"], [6, 2, 1, "", "start"]], "isaacgym.gymapi.Mat33": [[6, 2, 1, "", "x"], [6, 2, 1, "", "y"], [6, 2, 1, "", "z"]], "isaacgym.gymapi.Mat44": [[6, 2, 1, "", "w"], [6, 2, 1, "", "x"], [6, 2, 1, "", "y"], [6, 2, 1, "", "z"]], "isaacgym.gymapi.PerformanceTimers": [[6, 2, 1, "", "frame_idling"], [6, 2, 1, "", "graphics_image_retrieval"], [6, 2, 1, "", "graphics_sensor_rendering"], [6, 2, 1, "", "graphics_viewer_rendering"], [6, 2, 1, "", "physics_data_movement"], [6, 2, 1, "", "physics_sim"], [6, 2, 1, "", "total_time"]], "isaacgym.gymapi.PhysXParams": [[6, 2, 1, "", "always_use_articulations"], [6, 2, 1, "", "bounce_threshold_velocity"], [6, 2, 1, "", "contact_collection"], [6, 2, 1, "", "contact_offset"], [6, 2, 1, "", "default_buffer_size_multiplier"], [6, 2, 1, "", "friction_correlation_distance"], [6, 2, 1, "", "friction_offset_threshold"], [6, 2, 1, "", "max_depenetration_velocity"], [6, 2, 1, "", "max_gpu_contact_pairs"], [6, 2, 1, "", "num_position_iterations"], [6, 2, 1, "", "num_subscenes"], [6, 2, 1, "", "num_threads"], [6, 2, 1, "", "num_velocity_iterations"], [6, 2, 1, "", "rest_offset"], [6, 2, 1, "", "solver_type"], [6, 2, 1, "", "use_gpu"]], "isaacgym.gymapi.PlaneParams": [[6, 2, 1, "", "distance"], [6, 2, 1, "", "dynamic_friction"], [6, 2, 1, "", "normal"], [6, 2, 1, "", "restitution"], [6, 2, 1, "", "segmentation_id"], [6, 2, 1, "", "static_friction"]], "isaacgym.gymapi.Quat": [[6, 0, 1, "", "dtype"], [6, 3, 1, "", "from_axis_angle"], [6, 3, 1, "", "from_buffer"], [6, 3, 1, "", "from_euler_zyx"], [6, 3, 1, "", "inverse"], [6, 3, 1, "", "normalize"], [6, 3, 1, "", "rotate"], [6, 3, 1, "", "to_euler_zyx"], [6, 2, 1, "", "w"], [6, 2, 1, "", "x"], [6, 2, 1, "", "y"], [6, 2, 1, "", "z"]], "isaacgym.gymapi.RigidBodyProperties": [[6, 2, 1, "", "com"], [6, 2, 1, "", "flags"], [6, 2, 1, "", "inertia"], [6, 2, 1, "", "invInertia"], [6, 2, 1, "", "invMass"], [6, 2, 1, "", "mass"]], "isaacgym.gymapi.RigidBodyState": [[6, 0, 1, "", "dtype"], [6, 2, 1, "", "pose"], [6, 2, 1, "", "vel"]], "isaacgym.gymapi.RigidContact": [[6, 2, 1, "", "body0"], [6, 2, 1, "", "body1"], [6, 2, 1, "", "env0"], [6, 2, 1, "", "env1"], [6, 2, 1, "", "friction"], [6, 2, 1, "", "initial_overlap"], [6, 2, 1, "", "lambda"], [6, 2, 1, "", "lambda_friction"], [6, 2, 1, "", "local_pos0"], [6, 2, 1, "", "local_pos1"], [6, 2, 1, "", "min_dist"], [6, 2, 1, "", "normal"], [6, 2, 1, "", "offset0"], [6, 2, 1, "", "offset1"], [6, 2, 1, "", "rolling_friction"], [6, 2, 1, "", "torsion_friction"]], "isaacgym.gymapi.RigidShapeProperties": [[6, 2, 1, "", "compliance"], [6, 2, 1, "", "contact_offset"], [6, 2, 1, "", "filter"], [6, 2, 1, "", "friction"], [6, 2, 1, "", "rest_offset"], [6, 2, 1, "", "restitution"], [6, 2, 1, "", "rolling_friction"], [6, 2, 1, "", "thickness"], [6, 2, 1, "", "torsion_friction"]], "isaacgym.gymapi.SimParams": [[6, 2, 1, "", "dt"], [6, 2, 1, "", "enable_actor_creation_warning"], [6, 2, 1, "", "flex"], [6, 2, 1, "", "gravity"], [6, 2, 1, "", "num_client_threads"], [6, 2, 1, "", "physx"], [6, 2, 1, "", "stress_visualization"], [6, 2, 1, "", "stress_visualization_max"], [6, 2, 1, "", "stress_visualization_min"], [6, 2, 1, "", "substeps"], [6, 2, 1, "", "up_axis"], [6, 2, 1, "", "use_gpu_pipeline"]], "isaacgym.gymapi.SoftMaterial": [[6, 2, 1, "", "activation"], [6, 2, 1, "", "activationMax"], [6, 2, 1, "", "damping"], [6, 2, 1, "", "model"], [6, 2, 1, "", "poissons"], [6, 2, 1, "", "youngs"]], "isaacgym.gymapi.Tensor": [[6, 2, 1, "", "data_address"], [6, 2, 1, "", "data_ptr"], [6, 2, 1, "", "device"], [6, 2, 1, "", "dtype"], [6, 2, 1, "", "ndim"], [6, 2, 1, "", "own_data"], [6, 2, 1, "", "shape"]], "isaacgym.gymapi.Transform": [[6, 0, 1, "", "dtype"], [6, 3, 1, "", "from_buffer"], [6, 3, 1, "", "inverse"], [6, 2, 1, "", "p"], [6, 2, 1, "", "r"], [6, 3, 1, "", "transform_point"], [6, 3, 1, "", "transform_points"], [6, 3, 1, "", "transform_vector"], [6, 3, 1, "", "transform_vectors"]], "isaacgym.gymapi.TriangleMeshParams": [[6, 2, 1, "", "dynamic_friction"], [6, 2, 1, "", "nb_triangles"], [6, 2, 1, "", "nb_vertices"], [6, 2, 1, "", "restitution"], [6, 2, 1, "", "segmentation_id"], [6, 2, 1, "", "static_friction"], [6, 2, 1, "", "transform"]], "isaacgym.gymapi.Vec3": [[6, 3, 1, "", "cross"], [6, 3, 1, "", "dot"], [6, 0, 1, "", "dtype"], [6, 3, 1, "", "from_buffer"], [6, 3, 1, "", "length"], [6, 3, 1, "", "length_sq"], [6, 3, 1, "", "normalize"], [6, 2, 1, "", "x"], [6, 2, 1, "", "y"], [6, 2, 1, "", "z"]], "isaacgym.gymapi.Velocity": [[6, 2, 1, "", "angular"], [6, 0, 1, "", "dtype"], [6, 3, 1, "", "from_buffer"], [6, 2, 1, "", "linear"]], "isaacgym.gymapi.Version": [[6, 2, 1, "", "major"], [6, 2, 1, "", "minor"]], "isaacgym.gymapi.VhacdParams": [[6, 2, 1, "", "alpha"], [6, 2, 1, "", "beta"], [6, 2, 1, "", "concavity"], [6, 2, 1, "", "convex_hull_approximation"], [6, 2, 1, "", "convex_hull_downsampling"], [6, 2, 1, "", "max_convex_hulls"], [6, 2, 1, "", "max_num_vertices_per_ch"], [6, 2, 1, "", "min_volume_per_ch"], [6, 2, 1, "", "mode"], [6, 2, 1, "", "ocl_acceleration"], [6, 2, 1, "", "pca"], [6, 2, 1, "", "plane_downsampling"], [6, 2, 1, "", "project_hull_vertices"], [6, 2, 1, "", "resolution"]]}, "objtypes": {"0": "py:attribute", "1": "py:class", "2": "py:property", "3": "py:method"}, "objnames": {"0": ["py", "attribute", "Python attribute"], "1": ["py", "class", "Python class"], "2": ["py", "property", "Python property"], "3": ["py", "method", "Python method"]}, "titleterms": {"about": 0, "isaac": [0, 10, 12], "gym": [0, 4, 10, 11, 12], "what": [0, 11], "how": [0, 11], "doe": 0, "relat": 0, "omnivers": 0, "sim": 0, "The": [0, 20], "futur": 0, "api": [1, 4, 5, 10, 15, 16, 19, 21, 24], "refer": 1, "python": [2, 3, 4, 5, 6, 13], "constant": 2, "flag": 2, "enum": 3, "structur": 6, "bundl": 7, "asset": [7, 10, 14, 20], "articul": 7, "model": 7, "mjcf": 7, "ant": 7, "nv_ant": 7, "xml": 7, "humanoid": 7, "nv_humanoid": 7, "urdf": 7, "anym": 7, "anymal_b_simple_descript": 7, "franka": [7, 10], "panda": 7, "franka_descript": 7, "robot": 7, "franka_panda": 7, "kinova": 7, "jaco": 7, "kinova_descript": 7, "cabinet": 7, "sektion_cabinet_model": 7, "sektion_cabinet": 7, "textur": [7, 16], "exampl": [8, 9, 10, 13, 14], "reinforc": [9, 13], "learn": [9, 13], "program": [10, 17], "common": [10, 21, 23], "command": [10, 24], "line": [10, 16, 24], "option": [10, 14], "list": 10, "collis": 10, "filter": 10, "1080_balls_of_solitud": 10, "py": 10, "environ": [10, 11, 13, 20, 24], "info": 10, "asset_info": 10, "bodi": [10, 15, 19, 21], "physic": [10, 19, 21], "properti": [10, 14, 16, 19], "body_physics_prop": 10, "domain": 10, "random": 10, "domain_random": 10, "attractor": 10, "franka_attractor": 10, "graphic": [10, 16], "dof": [10, 19, 21], "control": [10, 19, 21], "dof_control": 10, "joint": [10, 11, 15, 19], "monkei": 10, "joint_monkei": 10, "math": [10, 18], "soft": 10, "soft_bodi": 10, "visual": [10, 16, 23], "transform": 10, "projectil": 10, "larg": 10, "mass": [10, 21], "ratio": 10, "test": [10, 13], "large_mass_ratio": 10, "kuka": 10, "bin": 10, "kuka_bin": 10, "pytorch": 10, "interop": 10, "interop_torch": 10, "ik": 10, "pick": 10, "franka_cube_ik": 10, "oper": 10, "space": 10, "franka_osc": 10, "appli": 10, "forc": [10, 15, 21, 22], "apply_forc": 10, "posit": 10, "apply_forces_at_po": 10, "multipl": [10, 11], "camera": [10, 16], "multiple_camera_env": 10, "up": [10, 13, 20], "axi": [10, 20], "test_graphics_up": 10, "materi": 10, "graphics_materi": 10, "actor": [10, 11, 14, 19, 20, 21], "scale": [10, 19], "actor_sc": 10, "terrain": [10, 22], "creation": 10, "terrain_cr": 10, "spheric": 10, "spherical_joint": 10, "frequent": 11, "ask": 11, "question": 11, "differ": 11, "between": 11, "dt": 11, "substep": 11, "happen": 11, "when": 11, "you": 11, "call": 11, "simul": [11, 19, 20, 21, 23], "do": 11, "handl": 11, "one": 11, "i": 11, "move": 11, "pose": 11, "veloc": 11, "etc": 11, "an": [11, 13], "welcom": 12, "s": 12, "document": 12, "user": [12, 13], "guid": 12, "indic": 12, "tabl": 12, "instal": 13, "prerequisit": 13, "set": 13, "packag": 13, "exist": 13, "new": [13, 24], "conda": 13, "docker": 13, "contain": 13, "rl": [13, 24], "simpl": 13, "troubleshoot": 13, "For": 13, "anaconda": 13, "load": [14, 20], "mesh": 14, "overrid": 14, "inerti": 14, "convex": 14, "decomposit": 14, "procedur": 14, "introspect": 14, "creat": [14, 19, 20], "limit": [14, 15, 21, 24], "relev": 14, "sensor": [15, 16, 21], "rigid": [15, 19, 21], "tensor": [15, 16, 19, 21], "imag": 16, "type": 16, "access": 16, "gpu": 16, "color": 16, "interact": 16, "reset": 16, "segment": 16, "id": 16, "light": 16, "intrins": 16, "step": 16, "util": 18, "aggreg": 19, "compon": 19, "degre": [19, 21], "freedom": [19, 21], "drive": 19, "mode": 19, "state": [19, 21], "setup": [20, 21], "paramet": [20, 23, 24], "ground": 20, "plane": 20, "run": 20, "ad": 20, "viewer": 20, "gui": 20, "custom": 20, "mous": 20, "keyboard": 20, "input": 20, "cleanup": 20, "root": 21, "all": 21, "jacobian": 21, "matric": 21, "contact": [21, 22], "problem": 21, "lifetim": 21, "net": 22, "issu": [22, 24], "tune": 23, "flex": 23, "physx": 23, "scene": 23, "debugg": 23, "pvd": 23, "releas": 24, "note": 24, "1": 24, "0": 24, "preview4": 24, "gener": 24, "featur": 24, "bug": 24, "fix": 24, "improv": 24, "preview3": 24, "driver": 24, "requir": 24, "chang": 24, "preview2": 24, "preview1": 24, "remov": 24, "known": 24}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 6, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1, "sphinx": 56}})