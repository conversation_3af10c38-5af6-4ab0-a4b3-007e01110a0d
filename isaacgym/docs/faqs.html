<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Frequently Asked Questions &mdash; <PERSON>ym  documentation</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/isaac_custom.css" type="text/css" />
      <link rel="stylesheet" href="_static/graphviz.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="_static/doctools.js"></script>
        <script async="async" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="prev" title="Python Constants and Flags" href="api/python/const_py.html" />
    <link href="_static/style.css" rel="stylesheet" type="text/css">

</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="index.html" class="icon icon-home"> Isaac Gym
            <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="about_gym.html">About Isaac Gym</a></li>
<li class="toctree-l1"><a class="reference internal" href="install.html">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="release-notes.html">Release Notes</a></li>
<li class="toctree-l1"><a class="reference internal" href="examples/index.html">Examples</a></li>
<li class="toctree-l1"><a class="reference internal" href="programming/index.html">Programming</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Frequently Asked Questions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#what-is-the-difference-between-dt-and-substep">What is the difference between dt and substep?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#what-happens-when-you-call-gym-simulate">What happens when you call gym.simulate()?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#how-do-you-handle-multiple-actors-in-one-environment">How do you handle multiple actors in one environment?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#how-do-i-move-the-pose-joints-velocity-etc-of-an-actor">How do I move the pose, joints, velocity, etc. of an actor?</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Isaac Gym</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home"></a> &raquo;</li>
      <li>Frequently Asked Questions</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="frequently-asked-questions">
<h1>Frequently Asked Questions<a class="headerlink" href="#frequently-asked-questions" title="Permalink to this heading"></a></h1>
<p>The Isaac Gym has an extremely large scope.  It deals with physics simulation, reinforcement learning, GPU parallelization, etc…
There’s a great deal going on “under the hood” and so it’s only reasonable that a user might have questions about what exactly is going on or how exactly to do certain common things.
In this section we address those questions that seem to occur the most often.</p>
<section id="what-is-the-difference-between-dt-and-substep">
<h2>What is the difference between dt and substep?<a class="headerlink" href="#what-is-the-difference-between-dt-and-substep" title="Permalink to this heading"></a></h2>
<p>dt is the time increment of the simulation.  Every time you step the simulation via <code class="docutils literal notranslate"><span class="pre">gym.simulate(...)</span></code> you more the sim forward in time by dt. The substep is how many slices this dt is split into in order to do the simulation.  The more substeps, the finer the slicing of time and the greater the accuracy of the simulation, but the more computationally intensive it becomes.  For example:  say your dt is 1/60 and your substep is 2.  When you call <code class="docutils literal notranslate"><span class="pre">gym.simulate(...)</span></code> the physics is propagated forward 1/60th of a second in two time steps of 1/120th of a second each.  You do not have access to the results of these 1/120th of a second time steps, only the total result at 1/60th of a second can be retrieved by calling <code class="docutils literal notranslate"><span class="pre">gym.fetch_results(...)</span></code>.  This is probably a consequence of certain numerical integration methods.</p>
<p>As a zeroth order example, lets say the integration method was Euler’s method.  If we have some function, <span class="math notranslate nohighlight">\(X(t)\)</span>, and we know <span class="math notranslate nohighlight">\(X(t = t_0)\)</span>, and we know the first derivative, <span class="math notranslate nohighlight">\(X'\)</span>, then we can estimate <span class="math notranslate nohighlight">\(X(t)\)</span> at some future time <span class="math notranslate nohighlight">\(t + \text{d}t\)</span> naively</p>
<p><span class="math notranslate nohighlight">\(X(t+\text{d}t) \approx X(t_0) + \text{d}t X'(t_0)\)</span></p>
<p>In the limit as  <span class="math notranslate nohighlight">\(\text{d}t\)</span> → 0 this relationship becomes exact.  However, in real life, <span class="math notranslate nohighlight">\(\text{d}t\)</span> is <em>never</em> zero and we will always have some form of integration error.  Suppose <span class="math notranslate nohighlight">\(\text{d}t = 1 , X(t_0) = 1, \text{and} X' = X\)</span>.  This means that <span class="math notranslate nohighlight">\(X(t)\)</span> is exactly <span class="math notranslate nohighlight">\(\text{e}^t\)</span>, and so at <span class="math notranslate nohighlight">\(t\)</span> = 1 our exact result should be e, or about 2.71828.   If we approximate <span class="math notranslate nohighlight">\(X(t=1)\)</span> in a single time step we get 1 + 1*1 = 2, more than 25% less than what it should be.  If we divide that single time step into two substeps we get</p>
<p><span class="math notranslate nohighlight">\(X(t + 0.5) \approx 1 + 0.5*1 = 1.5\)</span></p>
<p><span class="math notranslate nohighlight">\(X(t + 0.5 + 0.5) \approx 1.5 + 0.5*1.5 = 2.25\)</span></p>
<p>which is off by about 17%.  Much improved!  Euler’s method is sensitive to the size of dt and the number of substeps chosen and while Gym doesn’t use Euler’s method, even advanced numerical integration algorithms are sensitive to step size and the number of substeps taken.  You cannot escape integration error.</p>
<p>In general, dt should be much less than 1 and the number of substeps should be a small integer (2 or 4 is common).  If dt is too large you will see weird behavior in the simulation (things will explode).  If substeps is too large, the simulation will take an undesireably long time to run.</p>
</section>
<section id="what-happens-when-you-call-gym-simulate">
<h2>What happens when you call gym.simulate()?<a class="headerlink" href="#what-happens-when-you-call-gym-simulate" title="Permalink to this heading"></a></h2>
<p>When you call <code class="docutils literal notranslate"><span class="pre">gym.simulate(...)</span></code> you are stepping the simulation through time by an amount equal to dt.  As described above this simulation is usually divided into substeps for accuracy reasons.  To save memory, the simulation results for each substep are only temporarily saved, while the results at the end of dt are available to be fetched from the device.</p>
<p>It is entirely possible that simulating 1 second of the virtual world could take longer than 1 second of real time, but if you are using modern hardware this should only be a concern for the most complex of physics scenes (if you see this in practice and you aren’t trying to simulate an entire ocean of particles, then chances are high that there’s some other issue elsewhere in your code).  More likely than not, it will take significantly less than 1 second of real time to simulate 1 second of virtual time.  If the frame rate renders as fast as possible this would make things in the world appear to move much faster than they would in real life.  You can mitigate this by using the <code class="docutils literal notranslate"><span class="pre">gym.sync_frame_time(...)</span></code> python binding, which synchronizes the simulation to the rendered frame rate, and forces the program to wait until dt real time seconds have elapsed before continuing.</p>
</section>
<section id="how-do-you-handle-multiple-actors-in-one-environment">
<h2>How do you handle multiple actors in one environment?<a class="headerlink" href="#how-do-you-handle-multiple-actors-in-one-environment" title="Permalink to this heading"></a></h2>
<p>Isaac Gym is data oriented, and so all actors and bodies are accessed via their handles.  Currently these handles are unique on a per environment basis, but this could change in the future.  Presently, actors are only ‘aware’ of other actors and objects in their environment.  This is true for sensors as well.  If you render a camera sensor in env 1, all objects from env 2 will not appear in the render.  It is left to the user to determine how best to manage these handles and there are no real consequences to having multiple actors in a single environment.</p>
<p>API function calls for accessing actors, rigid bodies, and DOFs all require the corresponding actor handle, body handle, and environment pointer.  For example, suppose you have an environment with a Franka Panda robot arm and a cabinet.  Say you spawn a few hundred of these on your device.   If you want to access the end effector on a specific panda in a specific environment, you would need the handle for the end effector, the handle for the actor, and the env pointer to that environment.  Getting the actor handle and the env pointer are easy, you just save those at construction.  Getting the end effector is a bit trickier.  We know that the end effector is called “panda_hand” in the asset file, so we can use the <code class="docutils literal notranslate"><span class="pre">gym.find_actor_rigid_body_handle(...)</span></code> function to get the handle of that end effector.</p>
<p><code class="docutils literal notranslate"><span class="pre">effector_handle</span> <span class="pre">=</span> <span class="pre">gym.find_actor_rigid_body_handle(env_ptr,</span> <span class="pre">franka_actor_handle,</span> <span class="pre">&quot;panda_hand&quot;)</span></code></p>
<p>In short, multiple actors in a single environment is handled by handles.</p>
</section>
<section id="how-do-i-move-the-pose-joints-velocity-etc-of-an-actor">
<h2>How do I move the pose, joints, velocity, etc. of an actor?<a class="headerlink" href="#how-do-i-move-the-pose-joints-velocity-etc-of-an-actor" title="Permalink to this heading"></a></h2>
<p>There are a number of ways to do this.</p>
<p>First, we can simulate an applied torque on the joints.  This requires a few things; we need the joint “gear”, the power scale, a vector of the actions to take, the environment pointer, and the actor handle.  Applying torques to an actor is done by calling <code class="docutils literal notranslate"><span class="pre">gym.apply_actor_dof_efforts(...)</span></code>.  This function accepts the environment pointer, the actor handle, and an array of the torques to be applied (the efforts). The length of that array must match the number of articulatable joints in the actor.  This effort array is in units of Nm, and it is usually calculated by the product of the joint gear, the power scale, and a vector of actions (floats on -1,1 or 0,1 or whatever, depending on the actor).  The gear of the joint is the maximum torque that can be applied to the joint, while the power scale is just some factor to further scale that value.  When the <code class="docutils literal notranslate"><span class="pre">gym.simulate(...)</span></code> function is called, these torques will be applied to the joints, resulting in angular acceleration etc…</p>
<p>Second, we can specify inverse kinematic targets for the joints.  You can set position targets using <code class="docutils literal notranslate"><span class="pre">gym.set_actor_dof_position_targets(...)</span></code>.  When simulate is called, the actor joints will move based on their joint constraints and the effort DOF parameter to the target positions.</p>
<p>Third, you can just manually set the state of the bodies in the actor.  <strong>This can be dangerous!</strong>.  In order to do this you need to “respect” the joint constraints in the target state or you risk making your actor explode.  Because of this, you should only ever use this when you have a “cache” of target states you want to reset to (like resetting a training environment or whatever).  This is done by calling <code class="docutils literal notranslate"><span class="pre">gym.set_actor_rigid_body_states(...)</span></code>.  This function uses a numpy strucured array to define the target rigid body states.  The bindings define a set of dtypes that can be used to create numpy structured arrays for specific types of data: GymRigidBodyState is one of those types.  It has two fields, ‘pose’ and ‘vel’ for pose and velocity respectively.  Pose and velocity are actually GymTransform and GymVelocity respectively, which are also structured arrays (again, you don’t ever want to set this manually if you can avoid it).  This call also requires a gymapi state enum, which defines exactly what state data you want to set with this call, with the most common being <code class="docutils literal notranslate"><span class="pre">gymapi.STATE_ALL</span></code> meaning you want set both the body transforms and velocities.  When <code class="docutils literal notranslate"><span class="pre">gym.simulate(...)</span></code> is called, the actor bodies will teleport to the specified state instantaneously.  The best way to use this function is to call <code class="docutils literal notranslate"><span class="pre">numpy.copy(gym.get_actor_rigid_body_states(...))</span></code> to save the state, and then use the “set” call to reset to that state some time later.</p>
<p>Finally, you can call <code class="docutils literal notranslate"><span class="pre">gym.set_actor_dof_states(...)</span></code> to manually set the states of the DOFs (position and velocity).  This is done just as above, and just like before <strong>it can also be very dangerous</strong> .  Use this to reset joints to a cached state that you copied and saved from before.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="api/python/const_py.html" class="btn btn-neutral float-left" title="Python Constants and Flags" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2019-2021, NVIDIA Corporation.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>