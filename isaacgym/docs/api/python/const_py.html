<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Python Constants and Flags &mdash; <PERSON>  documentation</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/isaac_custom.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/graphviz.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Frequently Asked Questions" href="../../faqs.html" />
    <link rel="prev" title="Python Enums" href="enum_py.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">

</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> Isaac Gym
            <img src="../../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../about_gym.html">About Isaac Gym</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../install.html">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../release-notes.html">Release Notes</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../examples/index.html">Examples</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../../programming/index.html">Programming</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../../programming/simsetup.html">Simulation Setup</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/assets.html">Assets</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/physics.html">Physics Simulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/tensors.html">Tensor API</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/forcesensors.html">Force Sensors</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/tuning.html">Simulation Tuning</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/math.html">Math Utilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/graphics.html">Graphics and Camera Sensors</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../programming/terrain.html">Terrains</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="../index.html">API Reference</a><ul class="current">
<li class="toctree-l3 current"><a class="reference internal" href="index.html">Python API</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="gym_py.html">Python Gym API</a></li>
<li class="toctree-l4"><a class="reference internal" href="struct_py.html">Python Structures</a></li>
<li class="toctree-l4"><a class="reference internal" href="enum_py.html">Python Enums</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">Python Constants and Flags</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../faqs.html">Frequently Asked Questions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">Isaac Gym</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../../programming/index.html">Programming</a> &raquo;</li>
          <li><a href="../index.html">API Reference</a> &raquo;</li>
          <li><a href="index.html">Python API</a> &raquo;</li>
      <li>Python Constants and Flags</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="python-constants-and-flags">
<h1>Python Constants and Flags<a class="headerlink" href="#python-constants-and-flags" title="Permalink to this heading"></a></h1>
<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.INVALID_HANDLE">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">INVALID_HANDLE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">-1</span></em><a class="headerlink" href="#isaacgym.gymapi.INVALID_HANDLE" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<p>Default viewer size:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.DEFAULT_VIEWER_WIDTH">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">DEFAULT_VIEWER_WIDTH</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1600</span></em><a class="headerlink" href="#isaacgym.gymapi.DEFAULT_VIEWER_WIDTH" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.DEFAULT_VIEWER_HEIGHT">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">DEFAULT_VIEWER_HEIGHT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">900</span></em><a class="headerlink" href="#isaacgym.gymapi.DEFAULT_VIEWER_HEIGHT" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<p>State flags:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.STATE_NONE">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">STATE_NONE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#isaacgym.gymapi.STATE_NONE" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.STATE_POS">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">STATE_POS</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#isaacgym.gymapi.STATE_POS" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.STATE_VEL">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">STATE_VEL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#isaacgym.gymapi.STATE_VEL" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.STATE_ALL">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">STATE_ALL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">3</span></em><a class="headerlink" href="#isaacgym.gymapi.STATE_ALL" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<p>Rigid body flags:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.RIGID_BODY_NONE">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">RIGID_BODY_NONE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#isaacgym.gymapi.RIGID_BODY_NONE" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.RIGID_BODY_DISABLE_GRAVITY">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">RIGID_BODY_DISABLE_GRAVITY</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#isaacgym.gymapi.RIGID_BODY_DISABLE_GRAVITY" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.RIGID_BODY_DISABLE_SIMULATION">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">RIGID_BODY_DISABLE_SIMULATION</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">PhysX</span> <span class="pre">only</span></span></em><span class="sig-paren">)</span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#isaacgym.gymapi.RIGID_BODY_DISABLE_SIMULATION" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<p>Attractor axis flags:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_NONE">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_NONE</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">0</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_NONE" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_X">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_X</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">1</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_X" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_Y">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_Y</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">2</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_Y" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_Z">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_Z</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">4</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_Z" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_TWIST">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_TWIST</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">8</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_TWIST" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_SWING_1">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_SWING_1</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">16</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_SWING_1" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_SWING_2">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_SWING_2</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">32</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_SWING_2" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_TRANSLATION">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_TRANSLATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">7</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_TRANSLATION" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_ROTATION">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_ROTATION</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">56</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_ROTATION" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="isaacgym.gymapi.AXIS_ALL">
<span class="sig-prename descclassname"><span class="pre">gymapi.</span></span><span class="sig-name descname"><span class="pre">AXIS_ALL</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">63</span></em><a class="headerlink" href="#isaacgym.gymapi.AXIS_ALL" title="Permalink to this definition"></a></dt>
<dd></dd></dl>

</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="enum_py.html" class="btn btn-neutral float-left" title="Python Enums" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../../faqs.html" class="btn btn-neutral float-right" title="Frequently Asked Questions" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2019-2021, NVIDIA Corporation.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>