<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>About <PERSON>; <PERSON>  documentation</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/isaac_custom.css" type="text/css" />
      <link rel="stylesheet" href="_static/graphviz.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="_static/doctools.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Installation" href="install.html" />
    <link rel="prev" title="Welcome to Isaac Gym’s documentation!" href="index.html" />
    <link href="_static/style.css" rel="stylesheet" type="text/css">

</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="index.html" class="icon icon-home"> Isaac Gym
            <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">About Isaac Gym</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#what-is-isaac-gym">What is Isaac Gym?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#how-does-isaac-gym-relate-to-omniverse-and-isaac-sim">How does Isaac Gym relate to Omniverse and Isaac Sim?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#the-future-of-isaac-gym">The Future of Isaac Gym</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="install.html">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="release-notes.html">Release Notes</a></li>
<li class="toctree-l1"><a class="reference internal" href="examples/index.html">Examples</a></li>
<li class="toctree-l1"><a class="reference internal" href="programming/index.html">Programming</a></li>
<li class="toctree-l1"><a class="reference internal" href="faqs.html">Frequently Asked Questions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Isaac Gym</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home"></a> &raquo;</li>
      <li>About Isaac Gym</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="about-isaac-gym">
<h1>About Isaac Gym<a class="headerlink" href="#about-isaac-gym" title="Permalink to this heading"></a></h1>
<section id="what-is-isaac-gym">
<h2>What is Isaac Gym?<a class="headerlink" href="#what-is-isaac-gym" title="Permalink to this heading"></a></h2>
<p>Isaac Gym is NVIDIA’s prototype physics simulation environment for reinforcement learning research.</p>
<p>Isaac Gym allows developers to experiment with end-to-end GPU accelerated RL for physically based systems. Unlike other similar ‘gym’ style systems, in Isaac Gym, simulation can run on the GPU, storing results in GPU tensors rather than copying them back to CPU memory. A tensor-based API is provided to access these results, allowing RL observation and reward calculations to also take place on the GPU.</p>
<p>This combination permits thousand of environments to be simultaneously simulated on a single GPU, allowing experiments that previously may have required an entire data center to run on a single workstation.</p>
<p>Isaac Gym includes a basic PPO implementation and a straightforward RL task system that can be used with it, but users may substitute alternative task systems and RL algorithms as desired.</p>
<p>Core features of Isaac Gym include:</p>
<ul class="simple">
<li><p>Support for importing URDF and MJCF files</p></li>
<li><p>GPU accelerated tensor API for evaluating environment state and applying actions</p></li>
<li><p>Support for a variety of environment sensors - position, velocity, force, torque, etc</p></li>
<li><p>Runtime domain randomization of physics parameters</p></li>
<li><p>Jacobian / inverse kinematics support</p></li>
</ul>
</section>
<section id="how-does-isaac-gym-relate-to-omniverse-and-isaac-sim">
<h2>How does Isaac Gym relate to Omniverse and Isaac Sim?<a class="headerlink" href="#how-does-isaac-gym-relate-to-omniverse-and-isaac-sim" title="Permalink to this heading"></a></h2>
<p>Isaac Gym, in its current prototype incarnation, is a standalone system that does not directly interface with other NVIDIA simulation or robotics products, though it does use the same underlying PhysX simulation engine under the hood.</p>
<p>While <a class="reference external" href="https://developer.nvidia.com/nvidia-omniverse-platform">Omniverse</a> and the Omniverse-based <a class="reference external" href="https://developer.nvidia.com/isaac-sim">Isaac Sim</a> use Pixar’s USD scene description language to describe the environments to be simulated, Isaac Gym environment setup requires the use of Python code and custom APIs, though it can also import robot descriptions from URDF and MJCF format files.</p>
<p>Rendering in Isaac Gym is relatively basic, and does not support either ray tracing or the more sophisticated synthetic data sensors provided in Omniverse.</p>
</section>
<section id="the-future-of-isaac-gym">
<h2>The Future of Isaac Gym<a class="headerlink" href="#the-future-of-isaac-gym" title="Permalink to this heading"></a></h2>
<p>The tensor-based API for physics simulation at the core of Isaac Gym will be made available in future releases of NVIDIA’s Omniverse Platform, however the APIs used for environment setup will switch over to being based on USD. During the course of migrating to Omniverse, some features and APIs will undoubtedly change, while new APIs to support additional Omniverse functionality such as advanced rendering and soft body simulation will also be added.</p>
<p>More information about the transition of Isaac Gym functionality into Omniverse will be shared in the NVIDIA Dev Talk Forums when it is available.</p>
<p>In the meantime, we invite researchers and academics to explore the potential of GPU-based reinforcement learning, and to provide feedback to us on your experience!</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Welcome to Isaac Gym’s documentation!" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="install.html" class="btn btn-neutral float-right" title="Installation" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2019-2021, NVIDIA Corporation.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>