<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Welcome to <PERSON>’s documentation! &mdash; <PERSON>ym  documentation</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/isaac_custom.css" type="text/css" />
      <link rel="stylesheet" href="_static/graphviz.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="_static/doctools.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="About Isaac Gym" href="about_gym.html" />
    <link href="_static/style.css" rel="stylesheet" type="text/css">

</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="#" class="icon icon-home"> Isaac Gym
            <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="about_gym.html">About Isaac Gym</a></li>
<li class="toctree-l1"><a class="reference internal" href="install.html">Installation</a></li>
<li class="toctree-l1"><a class="reference internal" href="release-notes.html">Release Notes</a></li>
<li class="toctree-l1"><a class="reference internal" href="examples/index.html">Examples</a></li>
<li class="toctree-l1"><a class="reference internal" href="programming/index.html">Programming</a></li>
<li class="toctree-l1"><a class="reference internal" href="faqs.html">Frequently Asked Questions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">Isaac Gym</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home"></a> &raquo;</li>
      <li>Welcome to Isaac Gym’s documentation!</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="welcome-to-isaac-gym-s-documentation">
<h1>Welcome to Isaac Gym’s documentation!<a class="headerlink" href="#welcome-to-isaac-gym-s-documentation" title="Permalink to this heading"></a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">User Guide:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="about_gym.html">About Isaac Gym</a><ul>
<li class="toctree-l2"><a class="reference internal" href="about_gym.html#what-is-isaac-gym">What is Isaac Gym?</a></li>
<li class="toctree-l2"><a class="reference internal" href="about_gym.html#how-does-isaac-gym-relate-to-omniverse-and-isaac-sim">How does Isaac Gym relate to Omniverse and Isaac Sim?</a></li>
<li class="toctree-l2"><a class="reference internal" href="about_gym.html#the-future-of-isaac-gym">The Future of Isaac Gym</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="install.html">Installation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="install.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="install.html#set-up-the-python-package">Set up the Python package</a></li>
<li class="toctree-l2"><a class="reference internal" href="install.html#testing-the-installation">Testing the installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="install.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="release-notes.html">Release Notes</a><ul>
<li class="toctree-l2"><a class="reference internal" href="release-notes.html#preview4">1.0.preview4</a></li>
<li class="toctree-l2"><a class="reference internal" href="release-notes.html#preview3">1.0.preview3</a></li>
<li class="toctree-l2"><a class="reference internal" href="release-notes.html#preview2">1.0.preview2</a></li>
<li class="toctree-l2"><a class="reference internal" href="release-notes.html#preview1">1.0.preview1</a></li>
<li class="toctree-l2"><a class="reference internal" href="release-notes.html#known-issues-and-limitations">Known Issues and Limitations</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="examples/index.html">Examples</a><ul>
<li class="toctree-l2"><a class="reference internal" href="examples/simple.html">Programming Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="examples/rl.html">Reinforcement Learning Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="examples/assets.html">Bundled Assets</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="programming/index.html">Programming</a><ul>
<li class="toctree-l2"><a class="reference internal" href="programming/simsetup.html">Simulation Setup</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/assets.html">Assets</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/physics.html">Physics Simulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/tensors.html">Tensor API</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/forcesensors.html">Force Sensors</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/tuning.html">Simulation Tuning</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/math.html">Math Utilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/graphics.html">Graphics and Camera Sensors</a></li>
<li class="toctree-l2"><a class="reference internal" href="programming/terrain.html">Terrains</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="faqs.html">Frequently Asked Questions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="faqs.html#what-is-the-difference-between-dt-and-substep">What is the difference between dt and substep?</a></li>
<li class="toctree-l2"><a class="reference internal" href="faqs.html#what-happens-when-you-call-gym-simulate">What happens when you call gym.simulate()?</a></li>
<li class="toctree-l2"><a class="reference internal" href="faqs.html#how-do-you-handle-multiple-actors-in-one-environment">How do you handle multiple actors in one environment?</a></li>
<li class="toctree-l2"><a class="reference internal" href="faqs.html#how-do-i-move-the-pose-joints-velocity-etc-of-an-actor">How do I move the pose, joints, velocity, etc. of an actor?</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Permalink to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="about_gym.html" class="btn btn-neutral float-right" title="About Isaac Gym" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2019-2021, NVIDIA Corporation.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>