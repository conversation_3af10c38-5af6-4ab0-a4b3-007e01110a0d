YCB models are licensed under the following term:

The YCB Object and Model Set: Towards Common Benchmarks for Manipulation Research
Be<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>
This site provides the data for the YCB Object and Model set.

Related publications:
* <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, ["Benchmarking in Manipulation Research: Using the Yale-CMU-Berkeley Object and Model Set,"](https://www.eng.yale.edu/grablab/pubs/Calli_RAM2015.pdf) in IEEE Robotics & Automation Magazine, vol. 22, no. 3, pp. 36-52, Sept. 2015. doi: [10.1109/MRA.2015.2448951](http://dx.doi.org/10.1109/MRA.2015.2448951)
* <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, ["The YCB object and Model set: Towards common benchmarks for manipulation research,"](https://www.eng.yale.edu/grablab/pubs/Calli_ICAR2015.pdf) International Conference on Advanced Robotics (ICAR), Istanbul, 2015, pp. 510-517. doi: [10.1109/ICAR.2015.7251504](http://dx.doi.org/10.1109/ICAR.2015.7251504)

For requesting the physical objects and detailed information about the project, please visit [ycbbenchmarks.org](http://ycbbenchmarks.org/).

Data are collected by two state of the art systems: UC Berkley's scanning rig and the Google scanner.
For the data collected with the UC Berkley's scanning rig (which is made up of RGB and RGB-D cameras and was also used for [BigBIRD](http://people.eecs.berkeley.edu/~pabbeel/papers/2014-ICRA-BigBIRD.pdf)), we provide:
* a mesh generated with Poisson reconstruction
* a mesh generated with volumetric range image integration
* textured versions of both meshes
* Kinbody files for using the meshes with OpenRAVE
* point cloud of the object generated by all the viewpoints
* 600 High-resolution RGB images
* 600 RGBD images

For the data collected with the Google scanner, we provide:
* 3 meshes generated (with 16k, 64k, and 512k polygons)
* textured versions of each mesh
* Kinbody files for using the meshes with OpenRAVE

Note that some objects, depending on their properties (e.g. transparency) may not have complete meshes. We plan to later release better meshes for these objects using more advanced algorithms.

You can download files for individual objects using the table below.

We also provide the following python scripts and ROS node for downloading and processing the data:
* [ycb_downloader.py](http://ycb-benchmarks.s3-website-us-east-1.amazonaws.com/scripts_to_publish/ycb_downloader.py)
* [ycb_generate_point_cloud.py](http://ycb-benchmarks.s3-website-us-east-1.amazonaws.com/scripts_to_publish/ycb_generate_point_cloud.py)
* [ycb_benchmarks_ros_node.zip](http://ycb-benchmarks.s3-website-us-east-1.amazonaws.com/scripts_to_publish/ycb_benchmarks_ros_node.zip)

For installing some required packages in Ubuntu, you can use the following command:

sudo apt-get install python-scipy python-numpy python-h5py

License for the data set: Creative Commons Attribution 4.0 International (CC BY 4.0)

https://creativecommons.org/licenses/by/4.0/

License for the code: MIT license


