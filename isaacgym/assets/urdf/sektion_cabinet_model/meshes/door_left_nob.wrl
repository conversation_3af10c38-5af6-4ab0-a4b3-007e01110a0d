#VRML V2.0 utf8
Group {
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 0.975 0.486 0.975
          specularColor 0.7 0.7 1.0
        }
      }
      geometry IndexedFaceSet {
        solid FALSE
        coord Coordinate {
          point [
0.010346 0.176606 0.184551,
0.010346 0.175985 0.188075,
0.016218 0.175985 0.188075,
0.016218 0.176606 0.184551,
0.010346 0.174195 0.191174,
0.016218 0.174195 0.191174,
0.010346 0.171454 0.193474,
0.016218 0.171454 0.193474,
0.010346 0.168092 0.194698,
0.016218 0.168092 0.194698,
0.010346 0.164514 0.194698,
0.016218 0.164514 0.194698,
0.010346 0.161151 0.193474,
0.016218 0.161151 0.193474,
0.010346 0.158410 0.191174,
0.016218 0.158410 0.191174,
0.010346 0.156621 0.188075,
0.016218 0.156621 0.188075,
0.010346 0.156000 0.184551,
0.016218 0.156000 0.184551,
0.010346 0.156621 0.181028,
0.016218 0.156621 0.181028,
0.010346 0.158410 0.177929,
0.016218 0.158410 0.177929,
0.010346 0.161151 0.175629,
0.016218 0.161151 0.175629,
0.010346 0.164514 0.174405,
0.016218 0.164514 0.174405,
0.010346 0.168092 0.174405,
0.016218 0.168092 0.174405,
0.010346 0.171454 0.175629,
0.016218 0.171454 0.175629,
0.010346 0.174195 0.177929,
0.016218 0.174195 0.177929,
0.010346 0.175985 0.181027,
0.016218 0.175985 0.181027,
0.021149 0.177090 0.188478,
0.021149 0.177782 0.184551,
0.021149 0.175097 0.191930,
0.021149 0.172043 0.194493,
0.021149 0.168296 0.195857,
0.021149 0.164309 0.195857,
0.021149 0.160563 0.194493,
0.021149 0.157509 0.191930,
0.021149 0.155515 0.188478,
0.021149 0.154823 0.184551,
0.021149 0.155515 0.180625,
0.021149 0.157509 0.177172,
0.021149 0.160563 0.174610,
0.021149 0.164309 0.173246,
0.021149 0.168296 0.173246,
0.021149 0.172043 0.174610,
0.021149 0.175097 0.177172,
0.021149 0.177090 0.180625,
0.026081 0.181180 0.184551,
0.026081 0.180283 0.189640,
0.036057 0.180283 0.189640,
0.036057 0.181180 0.184551,
0.026081 0.177700 0.194114,
0.036057 0.177700 0.194114,
0.026081 0.173742 0.197436,
0.036057 0.173742 0.197436,
0.026081 0.168886 0.199203,
0.036057 0.168886 0.199203,
0.026081 0.163719 0.199203,
0.036057 0.163719 0.199203,
0.026081 0.158864 0.197436,
0.036057 0.158864 0.197436,
0.026081 0.154906 0.194114,
0.036057 0.154906 0.194114,
0.026081 0.152322 0.189640,
0.036057 0.152322 0.189640,
0.026081 0.151425 0.184551,
0.036057 0.151425 0.184551,
0.026081 0.152322 0.179463,
0.036057 0.152322 0.179463,
0.026081 0.154906 0.174988,
0.036057 0.154906 0.174988,
0.026081 0.158864 0.171667,
0.036057 0.158864 0.171667,
0.026081 0.163719 0.169900,
0.036057 0.163719 0.169900,
0.026081 0.168886 0.169900,
0.036057 0.168886 0.169900,
0.026081 0.173741 0.171667,
0.036057 0.173741 0.171667,
0.026081 0.177700 0.174988,
0.036057 0.177700 0.174988,
0.026081 0.180283 0.179463,
0.036057 0.180283 0.179463,
0.036843 0.179751 0.189446,
0.036843 0.180614 0.184551,
0.036843 0.177266 0.193750,
0.036843 0.173458 0.196945,
0.036843 0.168788 0.198645,
0.036843 0.163818 0.198645,
0.036843 0.159147 0.196945,
0.036843 0.155340 0.193750,
0.036843 0.152855 0.189446,
0.036843 0.151992 0.184551,
0.036843 0.152855 0.179657,
0.036843 0.155340 0.175352,
0.036843 0.159147 0.172158,
0.036843 0.163818 0.170458,
0.036843 0.168788 0.170458,
0.036843 0.173458 0.172158,
0.036843 0.177266 0.175352,
0.036843 0.179751 0.179657,
0.036843 0.166303 0.184551,

          ]
        }
        coordIndex [
0, 1, 2, -1,
0, 2, 3, -1,
1, 4, 5, -1,
1, 5, 2, -1,
4, 6, 7, -1,
4, 7, 5, -1,
6, 8, 9, -1,
6, 9, 7, -1,
8, 10, 11, -1,
8, 11, 9, -1,
10, 12, 13, -1,
10, 13, 11, -1,
12, 14, 15, -1,
12, 15, 13, -1,
14, 16, 17, -1,
14, 17, 15, -1,
16, 18, 19, -1,
16, 19, 17, -1,
18, 20, 21, -1,
18, 21, 19, -1,
20, 22, 23, -1,
20, 23, 21, -1,
22, 24, 25, -1,
22, 25, 23, -1,
24, 26, 27, -1,
24, 27, 25, -1,
26, 28, 29, -1,
26, 29, 27, -1,
28, 30, 31, -1,
28, 31, 29, -1,
30, 32, 33, -1,
30, 33, 31, -1,
32, 34, 35, -1,
32, 35, 33, -1,
34, 0, 3, -1,
34, 3, 35, -1,
2, 36, 37, -1,
2, 37, 3, -1,
5, 38, 36, -1,
5, 36, 2, -1,
7, 39, 38, -1,
7, 38, 5, -1,
9, 40, 39, -1,
9, 39, 7, -1,
11, 41, 40, -1,
11, 40, 9, -1,
13, 42, 41, -1,
13, 41, 11, -1,
15, 43, 42, -1,
15, 42, 13, -1,
17, 44, 43, -1,
17, 43, 15, -1,
19, 45, 44, -1,
19, 44, 17, -1,
21, 46, 45, -1,
21, 45, 19, -1,
23, 47, 46, -1,
23, 46, 21, -1,
25, 48, 47, -1,
25, 47, 23, -1,
27, 49, 48, -1,
27, 48, 25, -1,
29, 50, 49, -1,
29, 49, 27, -1,
31, 51, 50, -1,
31, 50, 29, -1,
33, 52, 51, -1,
33, 51, 31, -1,
35, 53, 52, -1,
35, 52, 33, -1,
3, 37, 53, -1,
3, 53, 35, -1,
54, 55, 56, -1,
54, 56, 57, -1,
55, 58, 59, -1,
55, 59, 56, -1,
58, 60, 61, -1,
58, 61, 59, -1,
60, 62, 63, -1,
60, 63, 61, -1,
62, 64, 65, -1,
62, 65, 63, -1,
64, 66, 67, -1,
64, 67, 65, -1,
66, 68, 69, -1,
66, 69, 67, -1,
68, 70, 71, -1,
68, 71, 69, -1,
70, 72, 73, -1,
70, 73, 71, -1,
72, 74, 75, -1,
72, 75, 73, -1,
74, 76, 77, -1,
74, 77, 75, -1,
76, 78, 79, -1,
76, 79, 77, -1,
78, 80, 81, -1,
78, 81, 79, -1,
80, 82, 83, -1,
80, 83, 81, -1,
82, 84, 85, -1,
82, 85, 83, -1,
84, 86, 87, -1,
84, 87, 85, -1,
86, 88, 89, -1,
86, 89, 87, -1,
88, 54, 57, -1,
88, 57, 89, -1,
56, 90, 91, -1,
56, 91, 57, -1,
59, 92, 90, -1,
59, 90, 56, -1,
61, 93, 92, -1,
61, 92, 59, -1,
63, 94, 93, -1,
63, 93, 61, -1,
65, 95, 94, -1,
65, 94, 63, -1,
67, 96, 95, -1,
67, 95, 65, -1,
69, 97, 96, -1,
69, 96, 67, -1,
71, 98, 97, -1,
71, 97, 69, -1,
73, 99, 98, -1,
73, 98, 71, -1,
75, 100, 99, -1,
75, 99, 73, -1,
77, 101, 100, -1,
77, 100, 75, -1,
79, 102, 101, -1,
79, 101, 77, -1,
81, 103, 102, -1,
81, 102, 79, -1,
83, 104, 103, -1,
83, 103, 81, -1,
85, 105, 104, -1,
85, 104, 83, -1,
87, 106, 105, -1,
87, 105, 85, -1,
89, 107, 106, -1,
89, 106, 87, -1,
57, 91, 107, -1,
57, 107, 89, -1,
91, 90, 108, -1,
90, 92, 108, -1,
92, 93, 108, -1,
93, 94, 108, -1,
94, 95, 108, -1,
95, 96, 108, -1,
96, 97, 108, -1,
97, 98, 108, -1,
98, 99, 108, -1,
99, 100, 108, -1,
100, 101, 108, -1,
101, 102, 108, -1,
102, 103, 108, -1,
103, 104, 108, -1,
104, 105, 108, -1,
105, 106, 108, -1,
106, 107, 108, -1,
107, 91, 108, -1,
55, 54, 37, -1,
55, 37, 36, -1,
58, 55, 36, -1,
58, 36, 38, -1,
60, 58, 38, -1,
60, 38, 39, -1,
62, 60, 39, -1,
62, 39, 40, -1,
64, 62, 40, -1,
64, 40, 41, -1,
66, 64, 41, -1,
66, 41, 42, -1,
68, 66, 42, -1,
68, 42, 43, -1,
70, 68, 43, -1,
70, 43, 44, -1,
72, 70, 44, -1,
72, 44, 45, -1,
74, 72, 45, -1,
74, 45, 46, -1,
76, 74, 46, -1,
76, 46, 47, -1,
78, 76, 47, -1,
78, 47, 48, -1,
80, 78, 48, -1,
80, 48, 49, -1,
82, 80, 49, -1,
82, 49, 50, -1,
84, 82, 50, -1,
84, 50, 51, -1,
86, 84, 51, -1,
86, 51, 52, -1,
88, 86, 52, -1,
88, 52, 53, -1,
54, 88, 53, -1,
54, 53, 37, -1

        ]
      }
    }
  ]
}
