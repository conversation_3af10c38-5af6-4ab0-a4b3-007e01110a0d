#VRML V2.0 utf8
Group {
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 0.975 0.486 0.975
          specularColor 0.7 0.7 1.0
        }
      }
      geometry IndexedFaceSet {
        solid FALSE
        coord Coordinate {
          point [
-0.253246 -0.345280 -0.041860,
-0.253246 0.345280 -0.041860,
-0.253246 0.345280 0.042860,
-0.253246 -0.345280 0.042860,
0.254200 0.346280 -0.042860,
0.254200 -0.346280 -0.042860,
-0.254200 -0.346280 -0.042860,
-0.254200 0.346280 -0.042860,
0.254200 0.345280 0.042860,
0.254200 0.346280 0.042860,
-0.254200 0.346280 0.042860,
-0.254200 -0.346280 0.042860,
0.254200 -0.346280 0.042860,
0.254200 -0.345280 0.042860,
0.254200 0.345280 -0.041860,
0.254200 -0.345280 -0.041860,
-0.257500 -0.346280 0.042860,
-0.257500 -0.346280 -0.042860,
-0.257500 0.346280 -0.042860,
-0.257500 0.346280 0.042860,
0.254200 0.349580 0.042860,
-0.254200 0.349580 0.042860,
0.254200 0.349580 -0.042860,
0.254200 -0.379410 -0.061912,
0.254200 -0.379410 0.061912,
0.254200 0.379410 0.061912,
0.254200 0.379410 -0.061912,
0.273200 0.379410 0.061912,
0.273200 0.379410 -0.061912,
0.273200 -0.379410 0.061912,
0.273200 -0.379410 -0.061912,
-0.254200 -0.349575 0.042860,
0.254200 -0.349575 0.042860,
0.254200 -0.349575 -0.042860,
-0.254200 -0.349575 -0.042860,
-0.257471 -0.346280 -0.042860,
-0.257471 -0.346280 0.042860,
-0.254200 0.349580 -0.042860,
-0.257454 0.349580 0.042860,
-0.257454 0.346280 0.042860,
-0.257471 -0.349575 -0.042860,
-0.257471 -0.349575 0.042860,
-0.257454 0.346280 -0.042860,
-0.257454 0.349580 -0.042860,

          ]
        }
        coordIndex [
0, 1, 2, -1,
2, 3, 0, -1,
4, 5, 6, -1,
6, 7, 4, -1,
8, 9, 10, -1,
11, 12, 13, -1,
11, 13, 3, -1,
2, 8, 10, -1,
11, 3, 2, -1,
10, 11, 2, -1,
4, 9, 8, -1,
4, 8, 14, -1,
13, 12, 5, -1,
4, 14, 15, -1,
15, 13, 5, -1,
4, 15, 5, -1,
11, 6, 6, -1,
11, 6, 11, -1,
5, 12, 12, -1,
5, 12, 5, -1,
6, 11, 16, -1,
6, 16, 17, -1,
10, 7, 18, -1,
10, 18, 19, -1,
10, 9, 20, -1,
10, 20, 21, -1,
9, 4, 22, -1,
9, 22, 20, -1,
0, 15, 14, -1,
14, 1, 0, -1,
2, 1, 14, -1,
14, 8, 2, -1,
15, 0, 3, -1,
3, 13, 15, -1,
23, 24, 25, -1,
25, 26, 23, -1,
26, 25, 27, -1,
27, 28, 26, -1,
28, 27, 29, -1,
29, 30, 28, -1,
24, 23, 30, -1,
30, 29, 24, -1,
30, 23, 26, -1,
26, 28, 30, -1,
27, 25, 24, -1,
24, 29, 27, -1,
11, 6, 6, -1,
11, 6, 11, -1,
12, 11, 11, -1,
12, 11, 12, -1,
6, 5, 5, -1,
6, 5, 6, -1,
12, 11, 11, -1,
12, 11, 12, -1,
12, 11, 31, -1,
12, 31, 32, -1,
6, 5, 33, -1,
6, 33, 34, -1,
5, 12, 12, -1,
5, 12, 5, -1,
6, 5, 5, -1,
6, 5, 6, -1,
33, 32, 31, -1,
31, 34, 33, -1,
11, 6, 35, -1,
11, 35, 36, -1,
5, 12, 32, -1,
5, 32, 33, -1,
20, 22, 37, -1,
37, 21, 20, -1,
4, 7, 37, -1,
4, 37, 22, -1,
10, 21, 38, -1,
10, 38, 39, -1,
17, 16, 19, -1,
19, 18, 17, -1,
7, 6, 17, -1,
7, 17, 18, -1,
11, 10, 19, -1,
11, 19, 16, -1

        ]
      }
    }
  ]
}
