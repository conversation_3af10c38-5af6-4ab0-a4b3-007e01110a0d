{"Plugins": [{"Info": {"SdfMetadata": {"faceIndexPrimvar": {"appliesTo": ["attributes"], "default": "ptexFaceIndex", "documentation": "Specifies an array of face indices used for ptex mapping", "type": "token"}, "faceOffsetPrimvar": {"appliesTo": ["attributes"], "default": "ptexFaceOffset", "documentation": "Specifies the ptex face index offset for aggregated ptex files", "type": "token"}, "uvPrimvar": {"appliesTo": ["attributes"], "default": "", "documentation": "Specifies the UV primvar for texture mapping", "type": "token"}}, "Types": {"UsdImagingBasisCurvesAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "BasisCurves"}, "UsdImagingCapsuleAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Capsule"}, "UsdImagingConeAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Cone"}, "UsdImagingCoordSysAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "coordSys"}, "UsdImagingCubeAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "C<PERSON>"}, "UsdImagingCylinderAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "<PERSON><PERSON><PERSON>"}, "UsdImagingMaterialAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "Material"}, "UsdImagingMeshAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "<PERSON><PERSON>"}, "UsdImagingNurbsPatchAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "NurbsPatch"}, "UsdImagingPointsAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Points"}, "UsdImagingPointInstancerAdapter": {"bases": ["UsdImagingPrimAdapter"], "isInternal": true, "primTypeName": "PointInstancer"}, "UsdImagingSphereAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Sphere"}, "UsdImagingVolumeAdapter": {"bases": ["UsdImagingGprimAdapter"], "isInternal": true, "primTypeName": "Volume"}, "UsdImagingDomeLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "DomeLight"}, "UsdImagingRectLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "RectLight"}, "UsdImagingSphereLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "SphereLight"}, "UsdImagingCylinderLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "CylinderLight"}, "UsdImagingDiskLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "DiskLight"}, "UsdImagingDistantLightAdapter": {"bases": ["UsdImagingLightAdapter"], "isInternal": true, "primTypeName": "DistantLight"}}}, "LibraryPath": "../../libusdImaging.so", "Name": "usdImaging", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}