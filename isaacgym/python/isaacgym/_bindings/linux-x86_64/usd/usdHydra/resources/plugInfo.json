# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdHydraDiscoveryPlugin" : {
                        "bases": ["NdrDiscoveryPlugin"],
                        "displayName": "Discovery plugin for deprecated hydra shaders."
                    }
                }
            }, 
            "LibraryPath": "../../libusdHydra.so", 
            "Name": "usdHydra", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
