# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "bindMaterialAs": {
                        "appliesTo": [
                            "relationships"
                        ], 
                        "displayGroup": "Shading", 
                        "documentation": "Metadata authored on collection-based material binding relationship to indicate the strength of the binding relative to bindings authored on descendant prims.", 
                        "type": "token"
                    }, 
                    "connectability": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": "full", 
                        "displayGroup": "Shading", 
                        "documentation": "Metadata authored on UsdShadeInput's to specify what they can be connected to. Can be either \"full\" or \"interfaceOnly\". \"full\" implies that  the input can be connected to any other input or output.  \"interfaceOnly\" implies that the input can only connect to a NodeGraph Input (which represents an interface override, not a render-time dataflow connection), or another Input whose connectability is also \"interfaceOnly\".", 
                        "type": "token"
                    }, 
                    "outputName": {
                        "appliesTo": [
                            "relationships"
                        ], 
                        "displayGroup": "deprecated", 
                        "type": "token"
                    }, 
                    "renderType": {
                        "appliesTo": [
                            "properties"
                        ], 
                        "displayGroup": "Rendering", 
                        "type": "token"
                    }, 
                    "sdrMetadata": {
                        "appliesTo": [
                            "prims", 
                            "attributes"
                        ], 
                        "displayGroup": "Shading", 
                        "type": "dictionary"
                    }
                }, 
                "Types": {
                    "UsdShadeConnectableAPI": {
                        "alias": {
                            "UsdSchemaBase": "ConnectableAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdShadeCoordSysAPI": {
                        "alias": {
                            "UsdSchemaBase": "CoordSysAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdShadeMaterial": {
                        "alias": {
                            "UsdSchemaBase": "Material"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdShadeNodeGraph"
                        ]
                    }, 
                    "UsdShadeMaterialBindingAPI": {
                        "alias": {
                            "UsdSchemaBase": "MaterialBindingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdShadeNodeGraph": {
                        "alias": {
                            "UsdSchemaBase": "NodeGraph"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdShadeShader": {
                        "alias": {
                            "UsdSchemaBase": "Shader"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdShadeShaderDefParserPlugin": {
                        "bases": [
                            "NdrParserPlugin"
                        ], 
                        "displayName": "USD-based shader definition parser plugin"
                    }
                }
            }, 
            "LibraryPath": "../../libusdShade.so", 
            "Name": "usdShade", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
