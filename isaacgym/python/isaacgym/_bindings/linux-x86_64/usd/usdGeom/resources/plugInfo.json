# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "constraintTargetIdentifier": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": "", 
                        "documentation": "Unique identifier within a model's namespace for an matrix-valued attribute representing a constraint target", 
                        "type": "token"
                    }, 
                    "elementSize": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": 1, 
                        "displayGroup": "Primvars", 
                        "documentation": "The number of values in a primvar's value array that must be aggregated for each element on the primitive.", 
                        "type": "int"
                    }, 
                    "inactiveIds": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "int64listop"
                    }, 
                    "interpolation": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": "constant", 
                        "displayGroup": "Primvars", 
                        "documentation": "How a primvar interpolates across a primitive; equivalent to RenderMan's 'class specifier'", 
                        "type": "token"
                    }, 
                    "metersPerUnit": {
                        "appliesTo": [
                            "layers"
                        ], 
                        "default": 0.01, 
                        "displayGroup": "Stage", 
                        "type": "double"
                    }, 
                    "unauthoredValuesIndex": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": -1, 
                        "displayGroup": "Primvars", 
                        "documentation": "The index that represents unauthored values in the indices array of an indexed primvar.", 
                        "type": "int"
                    }, 
                    "upAxis": {
                        "appliesTo": [
                            "layers"
                        ], 
                        "default": "Y", 
                        "displayGroup": "Stage", 
                        "type": "token"
                    }
                }, 
                "Types": {
                    "UsdGeomBasisCurves": {
                        "alias": {
                            "UsdSchemaBase": "BasisCurves"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomCurves"
                        ]
                    }, 
                    "UsdGeomBoundable": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ]
                    }, 
                    "UsdGeomCamera": {
                        "alias": {
                            "UsdSchemaBase": "Camera"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ]
                    }, 
                    "UsdGeomCapsule": {
                        "alias": {
                            "UsdSchemaBase": "Capsule"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomCone": {
                        "alias": {
                            "UsdSchemaBase": "Cone"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomCube": {
                        "alias": {
                            "UsdSchemaBase": "Cube"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomCurves": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomCylinder": {
                        "alias": {
                            "UsdSchemaBase": "Cylinder"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomGprim": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ]
                    }, 
                    "UsdGeomImageable": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdGeomMesh": {
                        "alias": {
                            "UsdSchemaBase": "Mesh"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ]
                    }, 
                    "UsdGeomModelAPI": {
                        "alias": {
                            "UsdSchemaBase": "GeomModelAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdGeomMotionAPI": {
                        "alias": {
                            "UsdSchemaBase": "MotionAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdGeomNurbsCurves": {
                        "alias": {
                            "UsdSchemaBase": "NurbsCurves"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomCurves"
                        ]
                    }, 
                    "UsdGeomNurbsPatch": {
                        "alias": {
                            "UsdSchemaBase": "NurbsPatch"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ]
                    }, 
                    "UsdGeomPointBased": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomPointInstancer": {
                        "alias": {
                            "UsdSchemaBase": "PointInstancer"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomPoints": {
                        "alias": {
                            "UsdSchemaBase": "Points"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomPrimvarsAPI": {
                        "alias": {
                            "UsdSchemaBase": "PrimvarsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdGeomScope": {
                        "alias": {
                            "UsdSchemaBase": "Scope"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomImageable"
                        ]
                    }, 
                    "UsdGeomSphere": {
                        "alias": {
                            "UsdSchemaBase": "Sphere"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdGeomSubset": {
                        "alias": {
                            "UsdSchemaBase": "GeomSubset"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdGeomXform": {
                        "alias": {
                            "UsdSchemaBase": "Xform"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ]
                    }, 
                    "UsdGeomXformable": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomImageable"
                        ]
                    }
                }
            }, 
            "LibraryPath": "../../libusdGeom.so", 
            "Name": "usdGeom", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
