-- glslfx version 0.1

//
// Copyright 2018 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

-- configuration
{
    "techniques": {
        "default": {
            "ColorCorrectionVertex": {
                "source": [ "ColorCorrection.Vertex" ]
            },
            "ColorCorrectionFragment": {
                "source": [ "ColorCorrection.Fragment" ]
            }
        }
    }
}

-- glsl ColorCorrection.Vertex

#version 120

attribute vec4 position;
attribute vec2 uvIn;

varying vec2 uv;

void main(void)
{
    gl_Position = position;
    uv = uvIn;
}

-- glsl ColorCorrection.Fragment

varying vec2 uv;

uniform sampler2D colorIn;
uniform sampler3D LUT3dIn;

// Similar to D3DX_DXGIFormatConvert.inl, but branchless
// https://www.shadertoy.com/view/wds3zM
vec3 FloatToSRGB(vec3 val)
{
    val = mix((val * 12.92),
              (1.055 * pow(val, vec3(1.0/2.4)) - 0.055),
              step(0.0031308, val));
    return val;
}

// Forward declare because C++ code will append the OCIO fn at end of shader
#if defined(GLSLFX_USE_OCIO)
    vec4 OCIODisplay(vec4 inPixel, sampler3D lut3d);
#endif

void main(void)
{
    vec4 color = texture2D(colorIn, uv);

    #if defined(GLSLFX_USE_OCIO)
        color = OCIODisplay(color, LUT3dIn);
    #else
        // Only color, not alpha is gamma corrected!
        color.rgb = FloatToSRGB(color.rgb);
    #endif

    gl_FragColor = color;
}
