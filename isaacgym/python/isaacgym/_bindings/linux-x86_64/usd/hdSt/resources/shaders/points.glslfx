-- glslfx version 0.1

//
// Copyright 2016 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/points.glslfx

#import $TOOLS/hdSt/shaders/instancing.glslfx
#import $TOOLS/hdSt/shaders/terminals.glslfx
#import $TOOLS/hdSt/shaders/pointId.glslfx

--- --------------------------------------------------------------------------
-- glsl Point.Vertex

out VertexData
{
    vec4 Peye;
} outData;

// Fwd declare methods defined in pointId.glslfx, that are used below.
int GetPointId();
void ProcessPointId(int);

void main(void)
{
    MAT4 transform  = ApplyInstanceTransform(HdGet_transform());
    vec4 point     = vec4(HdGet_points().xyz, 1);

    outData.Peye = vec4(GetWorldToViewMatrix() * transform * point);

    ProcessPrimvars();

    gl_Position = vec4(GetProjectionMatrix() * outData.Peye);
    ApplyClipPlanes(outData.Peye);

    // scale width following prman's behavior
    vec4 w = vec4(transform * HdGet_widths() * normalize(vec4(1,1,1,0)));
    float width = length(w.xyz);

    // compute screenspace width
    vec4 diameter = vec4(GetProjectionMatrix() * vec4(width, 0, outData.Peye.z, 1));
    vec2 viewportScale = GetViewport().zw * 0.5;

    gl_PointSize = clamp(viewportScale.x * diameter.x/diameter.w,
                         HD_GL_POINT_SIZE_MIN, HD_GL_POINT_SIZE_MAX);

    ProcessPointId(GetPointId());
}

--- --------------------------------------------------------------------------
-- glsl Point.Fragment

in VertexData
{
    vec4 Peye;
} inData;

void main(void)
{
    vec3 Peye = inData.Peye.xyz / inData.Peye.w;
    // camera facing.
    vec3 Neye = vec3(0, 0, 1);

    vec4 color = vec4(0.5, 0.5, 0.5, 1);
#ifdef HD_HAS_displayColor
    color.rgb = HdGet_displayColor().rgb;
#endif
#ifdef HD_HAS_displayOpacity
    color.a = HdGet_displayOpacity().r;
#endif

    vec4 patchCoord = vec4(0);
    color = ShadingTerminal(vec4(Peye, 1), Neye, color, patchCoord);

    if (ShouldDiscardByAlpha(color)) {
        discard;
    }

    RenderOutput(vec4(Peye, 1), Neye, color, patchCoord);
}
