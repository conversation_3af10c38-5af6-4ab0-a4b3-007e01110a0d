-- glslfx version 0.1

//
// Copyright 2019 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/imageShader.glslfx

-- configuration
{
    "techniques": {
        "default": {
            "vertexShader" : {
                "source": [ "ImageShader.Vertex" ]
            },
            "fragmentShader" : {
                "source": [ "ImageShader.Fragment" ]
            }
        }
    }
}

--- --------------------------------------------------------------------------
-- glsl ImageShader.Vertex

out VertexData
{
    vec2 uv;
} outData;

void main(void)
{
    // Position the vertices to create a large-than-screen triangle.
    // Adjust the UVs of the triangle to have 0-1 fit the screen exactly.
    // 'st' is the geometric UV where the [bottom,left] returns [0, 0].
    // Unlike gl_fragCoord where the [bottom,left] defaults to [0.5, 0.5].
    //
    //    gl_VertexID=0 -> (-1,-1)
    //    gl_VertexID=1 -> ( 3,-1)
    //    gl_VertexID=2 -> (-1, 3)
    //
    //    glDrawArrays( GL_TRIANGLES, 0, 3 );
    //
    //    ID=2
    //    x,y=-1,3
    //    u,v=0,2
    //    |\
    //    |  \
    //    |    \
    //    |      \
    //    |--------\
    //    |        | \
    //    | screen |   \
    //    |        |     \
    //     ----------------
    //    ID=0             ID=1
    //    x,y=-1,-1        x,y=3,-1
    //    u,v=0,0          u,v=2,0
    //
    //
    float x = -1.0 + float(((gl_VertexID%3) & 1) << 2);
    float y = -1.0 + float(((gl_VertexID%3) & 2) << 1);
    outData.uv.x = (x+1.0) * 0.5;
    outData.uv.y = (y+1.0) * 0.5;

    gl_Position = vec4(x, y, 0, 1);
}

--- --------------------------------------------------------------------------
-- glsl ImageShader.Fragment

in VertexData
{
    vec2 uv;
} inData;

layout (location = 0) out vec4 colorOut;

void main(void)
{
    colorOut = imageShader(inData.uv);
}
