-- glslfx version 0.1

//
// Copyright 2016 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

-- configuration
{
    "techniques": {
        "default": {
            "ptexTextureSampler" : {
                "source": [
                    "GlopTexture.PtexTextureSampler"
                ]
            }
        }
    }
}

-- glsl GlopTexture.PtexTextureSampler

// ----- following code comes from OpenSubdiv/examples/common

struct PtexPacking {
    int page;
    int nMipmap;
    int uOffset;
    int vOffset;
    int adjSizeDiffs[4];
    int width;
    int height;
};

int computeMipmapOffsetU(int w, int level)
{
    int width = 1 << w;
    int m = (0x55555555 & (width | (width-1))) << (w&1);
    int x = ~((1 << (w -((level-1)&~1))) - 1);
    return (m & x) + ((level+1)&~1);
}

int computeMipmapOffsetV(int h, int level)
{
    int height = 1 << h;
    int m = (0x55555555 & (height-1)) << ((h+1)&1);;
    int x = ~((1 << (h - (level&~1))) - 1 );
    return (m & x) + (level&~1);
}

void evalQuadraticBSpline(float u, out float B[3], out float BU[3])
{
    B[0] = 0.5 * (u*u - 2.0*u + 1);
    B[1] = 0.5 + u - u*u;
    B[2] = 0.5 * u*u;

    BU[0] = u - 1.0;
    BU[1] = 1 - 2 * u;
    BU[2] = u;
}

PtexPacking getPtexPacking(isamplerBuffer packings, int faceID)
{
    PtexPacking packing;
    packing.page    = texelFetch(packings, faceID*6).x;
    packing.nMipmap = texelFetch(packings, faceID*6+1).x;
    packing.uOffset = texelFetch(packings, faceID*6+2).x;
    packing.vOffset = texelFetch(packings, faceID*6+3).x;
    int wh          = texelFetch(packings, faceID*6+5).x;
    packing.width   = 1 << (wh >> 8);
    packing.height  = 1 << (wh & 0xff);

    int adjSizeDiffs = texelFetch(packings, faceID*6+4).x;
    packing.adjSizeDiffs[0] = (adjSizeDiffs >> 12) & 0xf;
    packing.adjSizeDiffs[1] = (adjSizeDiffs >> 8) & 0xf;
    packing.adjSizeDiffs[2] = (adjSizeDiffs >> 4) & 0xf;
    packing.adjSizeDiffs[3] = (adjSizeDiffs >> 0) & 0xf;

    return packing;
}

PtexPacking getPtexPacking(isamplerBuffer packings, int faceID, int level)
{
    PtexPacking packing;
    packing.page    = texelFetch(packings, faceID*6).x;
    packing.nMipmap = texelFetch(packings, faceID*6+1).x;
    packing.uOffset = texelFetch(packings, faceID*6+2).x;
    packing.vOffset = texelFetch(packings, faceID*6+3).x;
    int sizeDiffs   = texelFetch(packings, faceID*6+4).x;
    int wh          = texelFetch(packings, faceID*6+5).x;
    int w = wh >> 8;
    int h = wh & 0xff;

    // clamp max level
    level = min(level, packing.nMipmap);

    packing.uOffset += computeMipmapOffsetU(w, level);
    packing.vOffset += computeMipmapOffsetV(h, level);
    packing.width = 1 << (w-level);
    packing.height = 1 << (h-level);

    return packing;
}

// ----------------------------------------------------------------------------
// Non-Mipmap Lookups
// ----------------------------------------------------------------------------

vec4 PtexLookupNearest(vec4 patchCoord,
                       sampler2DArray data,
                       isamplerBuffer packings)
{
    vec2 uv = clamp(patchCoord.xy, vec2(0), vec2(1));
    int faceID = int(patchCoord.w);
    PtexPacking ppack = getPtexPacking(packings, faceID);
    vec2 coords = vec2(uv.x * ppack.width + ppack.uOffset,
                       uv.y * ppack.height + ppack.vOffset);
    return texelFetch(data, ivec3(int(coords.x), int(coords.y), ppack.page), 0);
}

vec4 PtexLookupNearest(vec4 patchCoord,
                       int level,
                       sampler2DArray data,
                       isamplerBuffer packings)
{
    vec2 uv = clamp(patchCoord.xy, vec2(0), vec2(1));
    int faceID = int(patchCoord.w);
    PtexPacking ppack = getPtexPacking(packings, faceID, level);
    vec2 coords = vec2(uv.x * ppack.width + ppack.uOffset,
                       uv.y * ppack.height + ppack.vOffset);
    return texelFetch(data, ivec3(int(coords.x), int(coords.y), ppack.page), 0);
}

vec4 PtexLookupFast(vec4 patchCoord,
                    sampler2DArray data,
                    isamplerBuffer packings)
{
    vec2 uv = clamp(patchCoord.xy, vec2(0), vec2(1));
    int faceID = int(patchCoord.w);
    PtexPacking ppack = getPtexPacking(packings, faceID);

    ivec3 size = textureSize(data, 0);
    vec2 coords = vec2((uv.x * ppack.width + ppack.uOffset)/size.x,
                       (uv.y * ppack.height + ppack.vOffset)/size.y);
    return texture(data, vec3(coords.x, coords.y, ppack.page));
}

vec4 PtexLookupFast(vec4 patchCoord,
                    int level,
                    sampler2DArray data,
                    isamplerBuffer packings)
{
    vec2 uv = clamp(patchCoord.xy, vec2(0), vec2(1));
    int faceID = int(patchCoord.w);
    PtexPacking ppack = getPtexPacking(packings, faceID, level);

    ivec3 size = textureSize(data, 0);
    vec2 coords = vec2((uv.x * ppack.width + ppack.uOffset)/size.x,
                       (uv.y * ppack.height + ppack.vOffset)/size.y);
    return texture(data, vec3(coords.x, coords.y, ppack.page));
}

vec4 PtexLookup(vec4 patchCoord,
                int level,
                sampler2DArray data,
                isamplerBuffer packings)
{
    vec2 uv = clamp(patchCoord.xy, vec2(0), vec2(1));
    int faceID = int(patchCoord.w);
    PtexPacking ppack = getPtexPacking(packings, faceID, level);

    vec2 coords = vec2(uv.x * ppack.width + ppack.uOffset,
                       uv.y * ppack.height + ppack.vOffset);

    coords -= vec2(0.5, 0.5);

    int c0X = int(floor(coords.x));
    int c1X = int(ceil(coords.x));
    int c0Y = int(floor(coords.y));
    int c1Y = int(ceil(coords.y));

    float t = coords.x - float(c0X);
    float s = coords.y - float(c0Y);

    vec4 d0 = texelFetch(data, ivec3(c0X, c0Y, ppack.page), 0);
    vec4 d1 = texelFetch(data, ivec3(c0X, c1Y, ppack.page), 0);
    vec4 d2 = texelFetch(data, ivec3(c1X, c0Y, ppack.page), 0);
    vec4 d3 = texelFetch(data, ivec3(c1X, c1Y, ppack.page), 0);

    vec4 result = (1-t) * ((1-s)*d0 + s*d1) + t * ((1-s)*d2 + s*d3);

    return result;
}

vec4 PtexLookupQuadratic(out vec4 du,
                         out vec4 dv,
                         vec4 patchCoord,
                         int level,
                         sampler2DArray data,
                         isamplerBuffer packings)
{
    vec2 uv = clamp(patchCoord.xy, vec2(0), vec2(1));
    int faceID = int(patchCoord.w);
    PtexPacking ppack = getPtexPacking(packings, faceID, level);

    vec2 coords = vec2(uv.x * ppack.width + ppack.uOffset,
                       uv.y * ppack.height + ppack.vOffset);

    coords -= vec2(0.5, 0.5);

    int cX = int(round(coords.x));
    int cY = int(round(coords.y));

    float x = 0.5 - (float(cX) - coords.x);
    float y = 0.5 - (float(cY) - coords.y);

    vec4 d[9];
    d[0] = texelFetch(data, ivec3(cX-1, cY-1, ppack.page), 0);
    d[1] = texelFetch(data, ivec3(cX-1, cY-0, ppack.page), 0);
    d[2] = texelFetch(data, ivec3(cX-1, cY+1, ppack.page), 0);
    d[3] = texelFetch(data, ivec3(cX-0, cY-1, ppack.page), 0);
    d[4] = texelFetch(data, ivec3(cX-0, cY-0, ppack.page), 0);
    d[5] = texelFetch(data, ivec3(cX-0, cY+1, ppack.page), 0);
    d[6] = texelFetch(data, ivec3(cX+1, cY-1, ppack.page), 0);
    d[7] = texelFetch(data, ivec3(cX+1, cY-0, ppack.page), 0);
    d[8] = texelFetch(data, ivec3(cX+1, cY+1, ppack.page), 0);

    float B[3], D[3];
    vec4 BUCP[3] = vec4[3](vec4(0,0,0,0), vec4(0,0,0,0), vec4(0,0,0,0)),
         DUCP[3] = vec4[3](vec4(0,0,0,0), vec4(0,0,0,0), vec4(0,0,0,0));
    evalQuadraticBSpline(y, B, D);

    for (int i = 0; i < 3; ++i) {
        for (int j = 0; j < 3; j++) {
            vec4 A = d[i*3+j];
            BUCP[i] += A * B[j];
            DUCP[i] += A * D[j];
        }
    }

    evalQuadraticBSpline(x, B, D);

    vec4 result = vec4(0);
    du = vec4(0);
    dv = vec4(0);
    for (int i = 0; i < 3; ++i) {
        result += B[i] * BUCP[i];
        du += D[i] * BUCP[i];
        dv += B[i] * DUCP[i];
    }

    du *= ppack.width;
    dv *= ppack.height;

    return result;
}

// ----------------------------------------------------------------------------
// MipMap Lookups
// ----------------------------------------------------------------------------
vec4 PtexMipmapLookupNearest(vec4 patchCoord,
                             float level,
                             sampler2DArray data,
                             isamplerBuffer packings)
{
#if defined(SEAMLESS_MIPMAP)
    // diff level
    int faceID = int(patchCoord.w);
    vec2 uv = patchCoord.xy;
    PtexPacking packing = getPtexPacking(packings, faceID);
    level += mix(mix(packing.adjSizeDiffs[0], packing.adjSizeDiffs[1], uv.x),
                 mix(packing.adjSizeDiffs[3], packing.adjSizeDiffs[2], uv.x),
                 uv.y);
#endif

    int levelm = int(floor(level));
    int levelp = int(ceil(level));
    float t = level - float(levelm);

    vec4 result = (1-t) * PtexLookupNearest(patchCoord, levelm, data, packings)
        + t * PtexLookupNearest(patchCoord, levelp, data, packings);
    return result;
}


vec4 PtexMipmapLookup(vec4 patchCoord,
                      float level,
                      sampler2DArray data,
                      isamplerBuffer packings)
{
#if defined(SEAMLESS_MIPMAP)
    // diff level
    int faceID = int(patchCoord.w);
    vec2 uv = patchCoord.xy;
    PtexPacking packing = getPtexPacking(packings, faceID);
    level += mix(mix(packing.adjSizeDiffs[0], packing.adjSizeDiffs[1], uv.x),
                 mix(packing.adjSizeDiffs[3], packing.adjSizeDiffs[2], uv.x),
                 uv.y);
#endif

    int levelm = int(floor(level));
    int levelp = int(ceil(level));
    float t = level - float(levelm);

    vec4 result = (1-t) * PtexLookup(patchCoord, levelm, data, packings)
        + t * PtexLookup(patchCoord, levelp, data, packings);
    return result;
}

vec4 PtexMipmapLookupQuadratic(out vec4 du,
                               out vec4 dv,
                               vec4 patchCoord,
                               float level,
                               sampler2DArray data,
                               isamplerBuffer packings)
{
#if defined(SEAMLESS_MIPMAP)
    // diff level
    int faceID = int(patchCoord.w);
    vec2 uv = patchCoord.xy;
    PtexPacking packing = getPtexPacking(packings, faceID);
    level += mix(mix(packing.adjSizeDiffs[0], packing.adjSizeDiffs[1], uv.x),
                 mix(packing.adjSizeDiffs[3], packing.adjSizeDiffs[2], uv.x),
                 uv.y);
#endif

    int levelm = int(floor(level));
    int levelp = int(ceil(level));
    float t = level - float(levelm);

    vec4 du0, du1, dv0, dv1;
    vec4 r0 = PtexLookupQuadratic(du0, dv0, patchCoord, levelm, data, packings);
    vec4 r1 = PtexLookupQuadratic(du1, dv1, patchCoord, levelp, data, packings);

    vec4 result = mix(r0, r1, t);
    du = mix(du0, du1, t);
    dv = mix(dv0, dv1, t);

    return result;
}

vec4 PtexMipmapLookupQuadratic(vec4 patchCoord,
                               float level,
                               sampler2DArray data,
                               isamplerBuffer packings)
{
    vec4 du, dv;
    return PtexMipmapLookupQuadratic(du, dv, patchCoord, level, data, packings);
}

// end from OpenSubdiv/examples/common

// ----------------------------------------------------------------------------

#ifndef GLOP_PTEX_TEXTURE_FACEOFFSET
#define GLOP_PTEX_TEXTURE_FACEOFFSET
uniform int ptexFaceOffset;
#endif

#define GLOP_PTEX_TEXTURE_DECLARE(textureName) \
    uniform sampler2DArray textureName##_Data;      \
    uniform isamplerBuffer textureName##_Packing;

#define GLOP_PTEX_TEXTURE_LOOKUP(textureName, patchCoord) \
    GlopPtexTextureLookup(textureName##_Data,             \
                          textureName##_Packing,          \
                          patchCoord)

vec4
GlopPtexTextureLookup(sampler2DArray data,
                      isamplerBuffer packings,
                      vec4 patchCoord)
{
    patchCoord.w += ptexFaceOffset;
    return PtexLookup(patchCoord,
                      /*level = */0,
                      data,
                      packings);
}


