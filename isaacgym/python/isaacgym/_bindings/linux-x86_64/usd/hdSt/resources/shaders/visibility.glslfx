-- glslfx version 0.1

//
// Copyright 2018 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/visibility.glslfx

--- --------------------------------------------------------------------------
-- glsl Visibility.Fragment.Topology

int GetElementID(); // code gen

void GetBitmaskBufferIndices(int id, out int arrayIndex, out int bitIndex)
{
    arrayIndex = id / 32;
    bitIndex   = id % 32;
}

bool IsBitSet(uint bitmask, int bitIndex)
{
    return bool(bitmask & (1 << bitIndex));
}

bool IsElementVisible()
{
#if defined(HD_HAS_elementsVisibility)
    // Element (face) visibility is encoded as an array of bitmasks (uint32)
    // with 1 bit per authored face.
    int elementId = GetElementID();
    // When rendering a mesh as points, element id doesn't make sense. Code
    // gen returns -1 as a fallback for this case.
    if (elementId != -1) {
        int arrayIndex, bitIndex;
        GetBitmaskBufferIndices(elementId, arrayIndex, bitIndex);
        uint ev = HdGet_elementsVisibility(arrayIndex);
        return IsBitSet(ev, bitIndex);
    }
#endif
    return true;
}

int GetPointId(); // pointId.glslfx

bool IsPointVisible()
{
#if defined(HD_HAS_pointsVisibility)
    // Point visibility is encoded as an array of bitmasks (uint32) with 1 bit
    // per unrefined vertex.
    int pointId = GetPointId();
    // When *not* rendering a mesh as points, we return -1 for the point id.
    // See PointId.Fragment.Fallback
    if (pointId != -1) {
        int arrayIndex, bitIndex;
        GetBitmaskBufferIndices(pointId, arrayIndex, bitIndex);
        uint pv = HdGet_pointsVisibility(arrayIndex);
        return IsBitSet(pv, bitIndex);
    }
#endif
    return true;
}

bool ShouldDiscardFragmentAccordingToTopology()
{
    if (!IsElementVisible()) {
        return true;
    }

    if (!IsPointVisible()) {
        return true;
    }

    return false;
}