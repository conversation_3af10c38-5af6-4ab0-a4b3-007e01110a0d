-- glslfx version 0.1

//
// Copyright 2018 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/pointId.glslfx

#import $TOOLS/hdx/shaders/selection.glslfx

--- --------------------------------------------------------------------------
-- glsl PointId.Vertex.None

int GetPointId()
{
    return -1;
}

float GetPointRasterSize(int pointId)
{
    return GetPointSize();
}

void ProcessPointId(int pointId)
{
    // do nothing
}

--- --------------------------------------------------------------------------
-- glsl PointId.Vertex.PointParam

// Fwd declare accessor method defined via code gen
int GetBaseVertexOffset();
int GetPointId()
{
    return gl_VertexID - GetBaseVertexOffset();
}

// Fwd declare selection decoder method defined in hdx/shaders/selection.glslfx
bool IsPointSelected(int);
float GetPointRasterSize(int pointId)
{
    return IsPointSelected(pointId)? 
                        GetPointSelectedSize() : GetPointSize();
}

// Plumb the pointId, for use in the FS.
// XXX: This works only because the TES and GS stages are disabled when
// rendering as points. If they are enabled, we need to add the plumbing.
flat out int vsPointId;
void ProcessPointId(int pointId)
{
    vsPointId = pointId;
}

--- --------------------------------------------------------------------------
-- glsl PointId.Fragment.Fallback

int GetPointId()
{
    return -1;
}

bool IsFragmentOnPoint() {
    return false;
}

--- --------------------------------------------------------------------------
-- glsl PointId.Fragment.PointParam

flat in int vsPointId;

int GetPointId()
{
    return vsPointId;
}

bool IsFragmentOnPoint() {
    return true;
}
