-- glslfx version 0.1

//
// Copyright 2016 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

---Percentage-Closer Filtering (PCF)

-- glsl PCF.ShadowFilterVertex
// ---------------------------------------------------------------------------
//  PCF compute shadow filter length in vertex shader
// ---------------------------------------------------------------------------
#define REQUIRE_SHADOW_FILTER_WIDTH

-- glsl PCF.ShadowFilterFragment
// ---------------------------------------------------------------------------
//  PCF shadow filtering in fragment shader
// ---------------------------------------------------------------------------

#if NUM_LIGHTS > 0

#define SHADOW_FILTER

#define JITTER_NUM_SAMPLES 16
#define PCF_NUM_SAMPLES JITTER_NUM_SAMPLES 

float shadowSample(int index, vec4 coord);
float shadowCompare(int index, vec4 coord);

vec2 jitter[JITTER_NUM_SAMPLES] =
    vec2[JITTER_NUM_SAMPLES](
        vec2(-0.94201624, -0.39906216),
        vec2( 0.94558609, -0.76890725),
        vec2(-0.09418410, -0.92938870),
        vec2( 0.34495938,  0.29387760),
        vec2(-0.91588581,  0.45771432),
        vec2(-0.81544232, -0.87912464),
        vec2(-0.38277543,  0.27676845),
        vec2( 0.97484398,  0.75648379),
        vec2( 0.44323325, -0.97511554),
        vec2( 0.53742981, -0.47373420),
        vec2(-0.26496911, -0.41893023),
        vec2( 0.79197514,  0.19090188),
        vec2(-0.24188840,  0.99706507),
        vec2(-0.81409955,  0.91437590),
        vec2( 0.19984126,  0.78641367),
        vec2( 0.14383161, -0.14100790)
    );

in vec2 FshadowFilterWidth[NUM_LIGHTS];

float
shadowFilter(int index, vec4 coord, vec4 Peye)
{
    vec2 filterWidth = FshadowFilterWidth[index];

    float sum = 0.0;
    for (int i=0; i<PCF_NUM_SAMPLES; ++i) { 
        vec2 offset = jitter[i] * filterWidth;
        sum += shadowCompare(index, vec4(coord.xy + offset, coord.z, coord.w));
    } 
    return sum / float(PCF_NUM_SAMPLES);
} 

#endif // NUM_LIGHTS > 0

-- glsl PCF.ShadowFilterFragmentOnly
// ---------------------------------------------------------------------------
//  PCF shadow filtering with compute filter length on the fly.
//  no vertex shader needed.
// ---------------------------------------------------------------------------

#if NUM_LIGHTS > 0

#define SHADOW_FILTER
#define JITTER_NUM_SAMPLES 16
#define PCF_NUM_SAMPLES JITTER_NUM_SAMPLES 

float shadowSample(int index, vec4 coord);
float shadowCompare(int index, vec4 coord);

vec2 jitter[JITTER_NUM_SAMPLES] =
    vec2[JITTER_NUM_SAMPLES](
        vec2(-0.94201624, -0.39906216),
        vec2( 0.94558609, -0.76890725),
        vec2(-0.09418410, -0.92938870),
        vec2( 0.34495938,  0.29387760),
        vec2(-0.91588581,  0.45771432),
        vec2(-0.81544232, -0.87912464),
        vec2(-0.38277543,  0.27676845),
        vec2( 0.97484398,  0.75648379),
        vec2( 0.44323325, -0.97511554),
        vec2( 0.53742981, -0.47373420),
        vec2(-0.26496911, -0.41893023),
        vec2( 0.79197514,  0.19090188),
        vec2(-0.24188840,  0.99706507),
        vec2(-0.81409955,  0.91437590),
        vec2( 0.19984126,  0.78641367),
        vec2( 0.14383161, -0.14100790)
    );

vec2 computeShadowFilterWidth(int index, vec4 Peye); // defined in simpleLighting.glslfx

float
shadowFilter(int index, vec4 coord, vec4 Peye)
{
    vec2 filterWidth = computeShadowFilterWidth(index, Peye);

    float sum = 0.0;
    for (int i=0; i<PCF_NUM_SAMPLES; ++i) { 
        vec2 offset = jitter[i] * filterWidth;
        sum += shadowCompare(index, vec4(coord.xy + offset, coord.z, coord.w));
    } 
    return sum / float(PCF_NUM_SAMPLES);
}


#endif // NUM_LIGHTS > 0
