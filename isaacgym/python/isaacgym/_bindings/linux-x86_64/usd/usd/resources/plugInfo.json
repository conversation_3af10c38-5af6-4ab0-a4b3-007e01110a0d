# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "apiSchemas": {
                        "appliesTo": "prims", 
                        "type": "tokenlistop"
                    }, 
                    "clipActive": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "double2[]"
                    }, 
                    "clipAssetPaths": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "asset[]"
                    }, 
                    "clipManifestAssetPath": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "asset"
                    }, 
                    "clipPrimPath": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "string"
                    }, 
                    "clipSets": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "stringlistop"
                    }, 
                    "clipTemplateAssetPath": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "string"
                    }, 
                    "clipTemplateEndTime": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "double"
                    }, 
                    "clipTemplateStartTime": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "double"
                    }, 
                    "clipTemplateStride": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "double"
                    }, 
                    "clipTimes": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "double2[]"
                    }, 
                    "clips": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "dictionary"
                    }
                }, 
                "Types": {
                    "UsdAPISchemaBase": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdSchemaBase"
                        ]
                    }, 
                    "UsdClipsAPI": {
                        "alias": {
                            "UsdSchemaBase": "ClipsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdCollectionAPI": {
                        "alias": {
                            "UsdSchemaBase": "CollectionAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdModelAPI": {
                        "alias": {
                            "UsdSchemaBase": "ModelAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdSchemaBase": {}, 
                    "UsdTyped": {
                        "bases": [
                            "UsdSchemaBase"
                        ]
                    }, 
                    "UsdUsdFileFormat": {
                        "bases": [
                            "SdfFileFormat"
                        ], 
                        "displayName": "USD File Format", 
                        "extensions": [
                            "usd"
                        ], 
                        "formatId": "usd", 
                        "primary": true, 
                        "target": "usd"
                    }, 
                    "UsdUsdaFileFormat": {
                        "bases": [
                            "SdfTextFileFormat"
                        ], 
                        "displayName": "USD Text File Format", 
                        "extensions": [
                            "usda"
                        ], 
                        "formatId": "usda", 
                        "primary": true, 
                        "target": "usd"
                    }, 
                    "UsdUsdcFileFormat": {
                        "bases": [
                            "SdfFileFormat"
                        ], 
                        "displayName": "USD Crate File Format", 
                        "extensions": [
                            "usdc"
                        ], 
                        "formatId": "usdc", 
                        "primary": true, 
                        "target": "usd"
                    }, 
                    "UsdUsdzFileFormat": {
                        "bases": [
                            "SdfFileFormat"
                        ], 
                        "displayName": "USDZ File Format", 
                        "extensions": [
                            "usdz"
                        ], 
                        "formatId": "usdz", 
                        "primary": true, 
                        "target": "usd"
                    }, 
                    "Usd_UsdzResolver": {
                        "bases": [
                            "ArPackageResolver"
                        ], 
                        "extensions": [
                            "usdz"
                        ]
                    }
                }
            }, 
            "LibraryPath": "../../libusd.so", 
            "Name": "usd", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
