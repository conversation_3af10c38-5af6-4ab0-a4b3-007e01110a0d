#usda 1.0
(
    "WARNING: THIS FILE IS GENERATED.  DO NOT EDIT."
    customLayerData = {
        string[] appliedAPISchemas = ["StatementsAPI", "RiMaterialAPI", "RiLightAPI", "RiLightFilterAPI", "RiLightPortalAPI", "RiSplineAPI", "RiTextureAPI"]
        string[] multipleApplyAPISchemas = []
    }
)

class "StatementsAPI" (
    doc = """Container namespace schema for all renderman statements.

    \\note The longer term goal is for clients to go directly to primvar
    or render-attribute API's, instead of using UsdRi StatementsAPI
    for inherited attributes.  Anticpating this, StatementsAPI
    can smooth the way via a few environment variables:

    * USDRI_STATEMENTS_WRITE_NEW_ENCODING: Causes StatementsAPI to write
      attributes to primvars in the \"ri:\" namespace.
    * USDRI_STATEMENTS_READ_OLD_ENCODING: Causes StatementsAPI to read
      old-style attributes instead of primvars in the \"ri:\"
      namespace.
    """
)
{
}

class RslShader "RslShader"
{
    uniform token info:id (
        doc = """The id is an identifier for the type or purpose of the 
        shader. E.g.: Texture or FractalFloat.
        The use of this id will depend on the render target: some will turn it
        into an actual shader path, some will use it to generate shader source 
        code dynamically.
        
        \\sa SetShaderId()
        """
    )
    uniform token info:implementationSource = "id" (
        allowedTokens = ["id", "sourceAsset", "sourceCode"]
        doc = """Specifies the attribute that should be consulted to get the 
        shader's implementation or its source code.

        * If set to \"id\", the \"info:id\" attribute's value is used to 
        determine the shader source from the shader registry.
        * If set to \"sourceAsset\", the resolved value of the \"info:sourceAsset\" 
        attribute corresponding to the desired implementation (or source-type)
        is used to locate the shader source.
        * If set to \"sourceCode\", the value of \"info:sourceCode\" attribute 
        corresponding to the desired implementation (or source type) is used as 
        the shader source.
        """
    )
    asset info:sloPath
}

class RisObject "RisObject" (
    doc = """\\deprecated Specialized RIS shader schemas have been deprecated in 
    favor of all shader prims being simple UsdShadeShader.
    Represents a ris object with connectable parameters. """
)
{
    asset info:argsPath
    asset info:filePath
    uniform token info:id (
        doc = """The id is an identifier for the type or purpose of the 
        shader. E.g.: Texture or FractalFloat.
        The use of this id will depend on the render target: some will turn it
        into an actual shader path, some will use it to generate shader source 
        code dynamically.
        
        \\sa SetShaderId()
        """
    )
    uniform token info:implementationSource = "id" (
        allowedTokens = ["id", "sourceAsset", "sourceCode"]
        doc = """Specifies the attribute that should be consulted to get the 
        shader's implementation or its source code.

        * If set to \"id\", the \"info:id\" attribute's value is used to 
        determine the shader source from the shader registry.
        * If set to \"sourceAsset\", the resolved value of the \"info:sourceAsset\" 
        attribute corresponding to the desired implementation (or source-type)
        is used to locate the shader source.
        * If set to \"sourceCode\", the value of \"info:sourceCode\" attribute 
        corresponding to the desired implementation (or source type) is used as 
        the shader source.
        """
    )
}

class RisPattern "RisPattern" (
    doc = """\\deprecated Specialized RIS shader schemas have been deprecated in 
    favor of all shader prims being simple UsdShadeShader.
    Represents a ris pattern object. Multiple of these can be assigned."""
)
{
    asset info:argsPath
    asset info:filePath
    uniform token info:id (
        doc = """The id is an identifier for the type or purpose of the 
        shader. E.g.: Texture or FractalFloat.
        The use of this id will depend on the render target: some will turn it
        into an actual shader path, some will use it to generate shader source 
        code dynamically.
        
        \\sa SetShaderId()
        """
    )
    uniform token info:implementationSource = "id" (
        allowedTokens = ["id", "sourceAsset", "sourceCode"]
        doc = """Specifies the attribute that should be consulted to get the 
        shader's implementation or its source code.

        * If set to \"id\", the \"info:id\" attribute's value is used to 
        determine the shader source from the shader registry.
        * If set to \"sourceAsset\", the resolved value of the \"info:sourceAsset\" 
        attribute corresponding to the desired implementation (or source-type)
        is used to locate the shader source.
        * If set to \"sourceCode\", the value of \"info:sourceCode\" attribute 
        corresponding to the desired implementation (or source type) is used as 
        the shader source.
        """
    )
}

class RisOslPattern "RisOslPattern" (
    doc = """\\deprecated Specialized RIS shader schemas have been deprecated in 
    favor of all shader prims being simple UsdShadeShader.
    Represents a ris osl pattern object."""
)
{
    asset info:argsPath
    asset info:filePath = @PxrOSL@ (
        hidden = true
    )
    uniform token info:id (
        doc = """The id is an identifier for the type or purpose of the 
        shader. E.g.: Texture or FractalFloat.
        The use of this id will depend on the render target: some will turn it
        into an actual shader path, some will use it to generate shader source 
        code dynamically.
        
        \\sa SetShaderId()
        """
    )
    uniform token info:implementationSource = "id" (
        allowedTokens = ["id", "sourceAsset", "sourceCode"]
        doc = """Specifies the attribute that should be consulted to get the 
        shader's implementation or its source code.

        * If set to \"id\", the \"info:id\" attribute's value is used to 
        determine the shader source from the shader registry.
        * If set to \"sourceAsset\", the resolved value of the \"info:sourceAsset\" 
        attribute corresponding to the desired implementation (or source-type)
        is used to locate the shader source.
        * If set to \"sourceCode\", the value of \"info:sourceCode\" attribute 
        corresponding to the desired implementation (or source type) is used as 
        the shader source.
        """
    )
    asset info:oslPath
}

class RisBxdf "RisBxdf" (
    doc = """\\deprecated Specialized RIS shader schemas have been deprecated in 
    favor of all shader prims being simple UsdShadeShader.
    Represents a ris bxdf object. One of these is assigned at one time."""
)
{
    asset info:argsPath
    asset info:filePath
    uniform token info:id (
        doc = """The id is an identifier for the type or purpose of the 
        shader. E.g.: Texture or FractalFloat.
        The use of this id will depend on the render target: some will turn it
        into an actual shader path, some will use it to generate shader source 
        code dynamically.
        
        \\sa SetShaderId()
        """
    )
    uniform token info:implementationSource = "id" (
        allowedTokens = ["id", "sourceAsset", "sourceCode"]
        doc = """Specifies the attribute that should be consulted to get the 
        shader's implementation or its source code.

        * If set to \"id\", the \"info:id\" attribute's value is used to 
        determine the shader source from the shader registry.
        * If set to \"sourceAsset\", the resolved value of the \"info:sourceAsset\" 
        attribute corresponding to the desired implementation (or source-type)
        is used to locate the shader source.
        * If set to \"sourceCode\", the value of \"info:sourceCode\" attribute 
        corresponding to the desired implementation (or source type) is used as 
        the shader source.
        """
    )
}

class RisIntegrator "RisIntegrator" (
    doc = "Integrator. Only one can be declared in a rib scene."
)
{
    asset argsPath
    asset filePath
}

class "RiMaterialAPI" (
    doc = """This API provides outputs that connect a material prim to prman 
    shaders and RIS objects."""
)
{
    token outputs:ri:displacement (
        displayGroup = "Outputs"
    )
    token outputs:ri:surface (
        displayGroup = "Outputs"
    )
    token outputs:ri:volume (
        displayGroup = "Outputs"
    )
}

class "RiLightAPI" (
    doc = """RiLightAPI is an API schema that provides an interface
    to add Renderman-specific attributes to lights."""
)
{
    float ri:intensityNearDist (
        displayGroup = "Refine"
        displayName = "Intensity Near Dist"
        doc = """Near distance between the point being illuminated and the
        light at which the sample doesn't get brighter. This may help you
        avoid hot spots and sampling issues where a light is near a
        surface."""
    )
    string ri:lightGroup (
        displayGroup = "Advanced"
        displayName = "Light Group"
        doc = """Specify the light group name used for light group LPEs.
        This is useful to generate per-light AOVs for later adjustment
        in compositing."""
    )
    int ri:sampling:fixedSampleCount (
        displayGroup = "Advanced"
        displayName = "Light Samples"
        doc = """Specifies an override of the number of light samples to be
        taken for this light source. If set to something other than zero,
        it will override the sampling performed by the integrator and can 
        result in a performance impact. For scenes that have lots of lights,
        resulting in some lights that are under-sampled, you may want to set
        it to non-zero."""
    )
    float ri:sampling:importanceMultiplier = 1 (
        displayGroup = "Advanced"
        displayName = "Importance Multiplier"
        doc = "Importance of this light for noise control."
    )
    bool ri:shadow:thinShadow (
        displayGroup = "Advanced"
        displayName = "Thin Shadow"
        doc = '''Enable thin shadow and disable refraction caustics for this
        light. This parameter will ignored if Trace Light Paths is
        enabled. This is a non-physical control that creates "fake"
        colored shadows for transmissive objects without needing to
        generate photons for caustics.'''
    )
    bool ri:trace:lightPaths (
        displayGroup = "Advanced"
        displayName = "Trace Light Paths"
        doc = """Enable light and photon tracing from this light. This
        value enforces a physically-based light and as a side-effect
        disables the above Shadows controls. Users may use this feature
        to selectively decide which lights emit photons when using the
        PxrVCM or PxrUPBP Integrators."""
    )
}

class "RiLightFilterAPI" (
    doc = "Renderman-specific attributes for light filters."
)
{
    token ri:combineMode (
        allowedTokens = ["multiply", "max", "min", "screen"]
        doc = """Specifies how this filter combines with others.
        Valid values are:

        - multiply: The results of filters are multiplied together
        - max: The maximum result of the filters is used.  This
          works best for grey-scale filters.
        - min: The minimum result of the filters is used. This
          works best for grey-scale filters.
        - screen: Similar to max, but combines gradients in a smoother
          way by using a screen operation:
          <pre>screen(a,b) = 1-(1-a)(1-b)</pre>
          This works best for grey-scale filters.

        Light filters on a light are grouped by their combine mode.
        Each group is executed and combined using that mode.  Then,
        the final results of each group are multiplied together.

        Fallback: multiply
        """
    )
    float ri:density (
        doc = "Scales the strength of the filter."
    )
    float ri:diffuse (
        doc = """A multiplier for the effect of this light on the diffuse
        response of materials.  This is a non-physical control."""
    )
    float ri:exposure = 0 (
        doc = "Exposure control for the multiplier."
    )
    float ri:intensity (
        doc = "Multipier for the diffuse and specular result."
    )
    bool ri:invert (
        doc = "When true, inverts the output of the light filter."
    )
    float ri:specular (
        doc = """A multiplier for the effect of this light on the specular
        response of materials.  This is a non-physical control."""
    )
}

class "RiLightPortalAPI" (
    doc = "Renderman-specific attributes for light portals."
)
{
    float ri:portal:intensity (
        displayGroup = "Basic"
        doc = """Intensity adjustment relative to the light intensity.
        This gets multiplied by the light's intensity and power"""
    )
    color3f ri:portal:tint (
        displayGroup = "Basic"
        displayName = "Color Tint"
        doc = "tint: This parameter tints the color from the dome texture."
    )
}

class "RiSplineAPI" (
    doc = '''RiSplineAPI is a general purpose API schema used to describe
    a named spline stored as a set of attributes on a prim.
    
    It is an add-on schema that can be applied many times to a prim with
    different spline names. All the attributes authored by the schema
    are namespaced under "$NAME:spline:", with the name of the
    spline providing a namespace for the attributes.

    The spline describes a 2D piecewise cubic curve with a position and
    value for each knot. This is chosen to give straightforward artistic
    control over the shape. The supported basis types are:

    - linear (UsdRiTokens->linear)
    - bspline (UsdRiTokens->bspline)
    - Catmull-Rom (UsdRiTokens->catmullRom)
    '''
)
{
}

class "RiTextureAPI" (
    doc = """RiTextureAPI is an API schema that provides an interface
    to add Renderman-specific attributes to adjust textures."""
)
{
    float ri:texture:gamma (
        doc = "Gamma-correct the texture"
    )
    float ri:texture:saturation (
        doc = "Adjust the texture's saturation"
    )
}

class PxrEnvDayLight "PxrEnvDayLight" (
    apiSchemas = ["CollectionAPI:lightLink", "CollectionAPI:shadowLink"]
)
{
    uniform bool collection:lightLink:includeRoot = 1
    uniform bool collection:shadowLink:includeRoot = 1
    color3f color = (1, 1, 1) (
        doc = "The color of emitted light, in energy-linear terms."
    )
    float colorTemperature = 6500 (
        displayName = "Color Temperature"
        doc = """Color temperature, in degrees Kelvin, representing the
        white point.  The default is a common white point, D65.  Lower
        values are warmer and higher values are cooler.  The valid range
        is from 1000 to 10000. Only takes effect when
        enableColorTemperature is set to true.  When active, the
        computed result multiplies against the color attribute.
        See UsdLuxBlackbodyTemperatureAsRgb()."""
    )
    int day = 1 (
        displayGroup = "MsApprox"
        displayName = "Day"
        doc = """day: Day of the month, 1 through 31.
        This is ignored if month is 0."""
    )
    float diffuse = 1 (
        displayName = "Diffuse Multiplier"
        doc = """A multiplier for the effect of this light on the diffuse
        response of materials.  This is a non-physical control."""
    )
    bool enableColorTemperature = 0 (
        displayName = "Enable Color Temperature"
        doc = "Enables using colorTemperature."
    )
    float exposure = 0 (
        doc = """Scales the power of the light exponentially as a power
        of 2 (similar to an F-stop control over exposure).  The result
        is multiplied against the intensity."""
    )
    rel filters (
        doc = "Relationship to the light filters that apply to this light."
    )
    float haziness = 2 (
        displayGroup = "MsApprox"
        displayName = "Haziness"
        doc = """haziness: The turbidity of the sky.  The lower limit of the
        model is 1.7 for  an exceptionally clear sky, and 10, for an
        nversion, is the upper  limit."""
    )
    float hour = 14.633333 (
        displayGroup = "MsApprox"
        displayName = "Hour"
        doc = """hour: Hours since midnight, local standard time.  May be
        fractional to include minutes and seconds.  If daylight saving time
        is in effect, subtract 1 to correct to standard time.  This is
        ignored if month is 0."""
    )
    float intensity = 1 (
        doc = "Scales the power of the light linearly."
    )
    float latitude = 47.602 (
        displayGroup = "MsApprox"
        displayName = "Latitude"
        doc = """latitude: Latitude in degrees.  Positive for north, negative
        for south.  Ranges frmo -90 to +90 degrees. This is ignored if
        month is 0."""
    )
    float longitude = -122.332 (
        displayGroup = "MsApprox"
        displayName = "Longitude"
        doc = """longitude: Longitude in degrees.  Positive for east, negative
        for west.  Ranges frmo -180 to +180 degrees.    This is ignored
        if month is 0."""
    )
    int month = 0 (
        displayGroup = "MsApprox"
        displayName = "Month"
        doc = """month: Month of the year, 1 through 12.      The default,
        0, means to use the explicitly given sun direction   instead of
        automatically computing it."""
    )
    bool normalize = 0 (
        displayName = "Normalize Power"
        doc = """Normalizes power by the surface area of the light.
        This makes it easier to independently adjust the power and shape
        of the light, by causing the power to not vary with the area or
        angular size of the light."""
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = '''Purpose is a concept we have found useful in our pipeline for 
        classifying geometry into categories that can each be independently
        included or excluded from traversals of prims on a stage, such as
        rendering or bounding-box computation traversals.  The fallback
        purpose, default indicates that a prim has "no special purpose"
        and should generally be included in all traversals.  Subtrees rooted
        at a prim with purpose render should generally only be included
        when performing a "final quality" render.  Subtrees rooted at a prim
        with purpose proxy should generally only be included when 
        performing a lightweight proxy render (such as openGL).  Finally,
        subtrees rooted at a prim with purpose guide should generally
        only be included when an interactive application has been explicitly
        asked to "show guides". 
        
        In the previous paragraph, when we say "subtrees rooted at a prim",
        we mean the most ancestral or tallest subtree that has an authored,
        non-default opinion.  If the purpose of </RootPrim> is set to 
        "render", then the effective purpose of </RootPrim/ChildPrim> will
        be "render" even if that prim has a different authored value for
        purpose.  <b>See ComputePurpose() for details of how purpose 
        inherits down namespace</b>.
        
        As demonstrated in UsdGeomBBoxCache, a traverser should be ready to 
        accept combinations of included purposes as an input.
        
        Purpose render can be useful in creating "light blocker"
        geometry for raytracing interior scenes.  Purposes render and
        proxy can be used together to partition a complicated model
        into a lightweight proxy representation for interactive use, and a
        fully realized, potentially quite heavy, representation for rendering.
        One can use UsdVariantSets to create proxy representations, but doing
        so requires that we recompose parts of the UsdStage in order to change
        to a different runtime level of detail, and that does not interact
        well with the needs of multithreaded rendering. Purpose provides us with
        a better tool for dynamic, interactive complexity management.'''
    )
    color3f skyTint = (1, 1, 1) (
        displayGroup = "MsApprox"
        displayName = "Sky Tint"
        doc = """skyTint: Tweak the sky's contribution and color.  The
        default, white (1,1,1),  gives results based on measured
        physical values."""
    )
    float specular = 1 (
        displayName = "Specular Multiplier"
        doc = """A multiplier for the effect of this light on the specular
        response of materials.  This is a non-physical control."""
    )
    vector3f sunDirection = (0, 0, 1) (
        displayGroup = "MsApprox"
        displayName = "Direction"
        doc = """sunDirection: The *apparent* direction towards the center
        of the sun. The zenith  is at +Y (for noon light) and the horizon
        is in the XZ plane (for  sunrise/set).  Note that the Y component
        must non- negative.  Ignored if a month is given."""
    )
    float sunSize = 1 (
        displayGroup = "MsApprox"
        displayName = "Sun Size"
        doc = """sunSize: Scale the apparent size of the sun in the sky.
        Leave at 1 for a  realistic sun size with an 0.55 degree
        angular diameter."""
    )
    color3f sunTint = (1, 1, 1) (
        displayGroup = "MsApprox"
        displayName = "Sun Tint"
        doc = """sunTint: Tweak the sun's contribution and color.  The
        default, white (1,1,1),  gives results based on measured
        physical values. Setting this to black removes the sun
        contribution."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
    int year = 2015 (
        displayGroup = "MsApprox"
        displayName = "Year"
        doc = "year: Four-digit year.    This is ignored if month is 0."
    )
    float zone = -8 (
        displayGroup = "MsApprox"
        displayName = "Time Zone"
        doc = """zone: Standard time zone offset from GMT/UTC in hours.
        Positive for east,  negative for west.  For example, this would
        be -8 for Pacific time.    This is ignored if month is 0."""
    )
}

class PxrAovLight "PxrAovLight" (
    apiSchemas = ["CollectionAPI:lightLink", "CollectionAPI:shadowLink"]
)
{
    string aovName = "" (
        displayGroup = "Advanced"
        displayName = "AOV Name"
        doc = "The name of the AOV to write to."
    )
    uniform bool collection:lightLink:includeRoot = 1
    uniform bool collection:shadowLink:includeRoot = 1
    color3f color = (1, 1, 1) (
        doc = "The color of emitted light, in energy-linear terms."
    )
    float colorTemperature = 6500 (
        displayName = "Color Temperature"
        doc = """Color temperature, in degrees Kelvin, representing the
        white point.  The default is a common white point, D65.  Lower
        values are warmer and higher values are cooler.  The valid range
        is from 1000 to 10000. Only takes effect when
        enableColorTemperature is set to true.  When active, the
        computed result multiplies against the color attribute.
        See UsdLuxBlackbodyTemperatureAsRgb()."""
    )
    float diffuse = 1 (
        displayName = "Diffuse Multiplier"
        doc = """A multiplier for the effect of this light on the diffuse
        response of materials.  This is a non-physical control."""
    )
    bool enableColorTemperature = 0 (
        displayName = "Enable Color Temperature"
        doc = "Enables using colorTemperature."
    )
    float exposure = 0 (
        doc = """Scales the power of the light exponentially as a power
        of 2 (similar to an F-stop control over exposure).  The result
        is multiplied against the intensity."""
    )
    rel filters (
        doc = "Relationship to the light filters that apply to this light."
    )
    bool inPrimaryHit = 1 (
        displayGroup = "Refine"
        displayName = "In Primvary Hit"
        doc = """If this is on, the usual mask of the illuminated objects
        is generated. If this is off, you can get a mask of only in the
        refraction or reflection."""
    )
    bool inReflection = 0 (
        displayGroup = "Refine"
        displayName = "In Reflection"
        doc = """If this is on, the rays are traced through the specular
        reflections to get  the masking signal.  Warning: this will
        require some amount of samples to get a clean mask."""
    )
    bool inRefraction = 0 (
        displayGroup = "Refine"
        displayName = "In Refraction"
        doc = """If this is on, the rays are traced through the glass
        refractions  to get the masking signal.  Warning: this will
        require some amount of samples to get a clean mask."""
    )
    float intensity = 1 (
        doc = "Scales the power of the light linearly."
    )
    bool invert = 0 (
        displayGroup = "Refine"
        displayName = "Invert"
        doc = "If this is on, it inverts the signal for the AOV."
    )
    bool normalize = 0 (
        displayName = "Normalize Power"
        doc = """Normalizes power by the surface area of the light.
        This makes it easier to independently adjust the power and shape
        of the light, by causing the power to not vary with the area or
        angular size of the light."""
    )
    bool onVolumeBoundaries = 1 (
        displayGroup = "Refine"
        displayName = "On Volume Boundaries"
        doc = """If this is on, the bounding box or shape of volumes will
        appear in the mask. Since this is not always desirable, this can
        be turned off."""
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = '''Purpose is a concept we have found useful in our pipeline for 
        classifying geometry into categories that can each be independently
        included or excluded from traversals of prims on a stage, such as
        rendering or bounding-box computation traversals.  The fallback
        purpose, default indicates that a prim has "no special purpose"
        and should generally be included in all traversals.  Subtrees rooted
        at a prim with purpose render should generally only be included
        when performing a "final quality" render.  Subtrees rooted at a prim
        with purpose proxy should generally only be included when 
        performing a lightweight proxy render (such as openGL).  Finally,
        subtrees rooted at a prim with purpose guide should generally
        only be included when an interactive application has been explicitly
        asked to "show guides". 
        
        In the previous paragraph, when we say "subtrees rooted at a prim",
        we mean the most ancestral or tallest subtree that has an authored,
        non-default opinion.  If the purpose of </RootPrim> is set to 
        "render", then the effective purpose of </RootPrim/ChildPrim> will
        be "render" even if that prim has a different authored value for
        purpose.  <b>See ComputePurpose() for details of how purpose 
        inherits down namespace</b>.
        
        As demonstrated in UsdGeomBBoxCache, a traverser should be ready to 
        accept combinations of included purposes as an input.
        
        Purpose render can be useful in creating "light blocker"
        geometry for raytracing interior scenes.  Purposes render and
        proxy can be used together to partition a complicated model
        into a lightweight proxy representation for interactive use, and a
        fully realized, potentially quite heavy, representation for rendering.
        One can use UsdVariantSets to create proxy representations, but doing
        so requires that we recompose parts of the UsdStage in order to change
        to a different runtime level of detail, and that does not interact
        well with the needs of multithreaded rendering. Purpose provides us with
        a better tool for dynamic, interactive complexity management.'''
    )
    float specular = 1 (
        displayName = "Specular Multiplier"
        doc = """A multiplier for the effect of this light on the specular
        response of materials.  This is a non-physical control."""
    )
    bool useColor = 0 (
        displayGroup = "Refine"
        displayName = "Use Color"
        doc = """If this is on, it outputs a RGB color image instead of a
        float image for the AOV."""
    )
    bool useThroughput = 1 (
        displayGroup = "Refine"
        displayName = "Use Throughput"
        doc = """If this is on, the values in the mask for the reflected
        or refracted rays will be affected by the strength of the reflection
        or refraction. This can lead to values below and above 1.0. Turn
        this off if you want a more solid mask."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class PxrIntMultLightFilter "PxrIntMultLightFilter" (
    apiSchemas = ["CollectionAPI:filterLink"]
    doc = "Multiplies the intensity of a given light."
)
{
    uniform bool collection:filterLink:includeRoot = 1
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = '''Purpose is a concept we have found useful in our pipeline for 
        classifying geometry into categories that can each be independently
        included or excluded from traversals of prims on a stage, such as
        rendering or bounding-box computation traversals.  The fallback
        purpose, default indicates that a prim has "no special purpose"
        and should generally be included in all traversals.  Subtrees rooted
        at a prim with purpose render should generally only be included
        when performing a "final quality" render.  Subtrees rooted at a prim
        with purpose proxy should generally only be included when 
        performing a lightweight proxy render (such as openGL).  Finally,
        subtrees rooted at a prim with purpose guide should generally
        only be included when an interactive application has been explicitly
        asked to "show guides". 
        
        In the previous paragraph, when we say "subtrees rooted at a prim",
        we mean the most ancestral or tallest subtree that has an authored,
        non-default opinion.  If the purpose of </RootPrim> is set to 
        "render", then the effective purpose of </RootPrim/ChildPrim> will
        be "render" even if that prim has a different authored value for
        purpose.  <b>See ComputePurpose() for details of how purpose 
        inherits down namespace</b>.
        
        As demonstrated in UsdGeomBBoxCache, a traverser should be ready to 
        accept combinations of included purposes as an input.
        
        Purpose render can be useful in creating "light blocker"
        geometry for raytracing interior scenes.  Purposes render and
        proxy can be used together to partition a complicated model
        into a lightweight proxy representation for interactive use, and a
        fully realized, potentially quite heavy, representation for rendering.
        One can use UsdVariantSets to create proxy representations, but doing
        so requires that we recompose parts of the UsdStage in order to change
        to a different runtime level of detail, and that does not interact
        well with the needs of multithreaded rendering. Purpose provides us with
        a better tool for dynamic, interactive complexity management.'''
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class PxrBarnLightFilter "PxrBarnLightFilter" (
    apiSchemas = ["CollectionAPI:filterLink"]
    doc = "Simulated geometric barn doors that control the spread of light."
)
{
    float analytic:apex = 0 (
        doc = "Shear the projection along the Y axis."
    )
    float analytic:density:exponent = 0 (
        doc = "Power exponent of the density interpolation."
    )
    float analytic:density:farDistance = 0 (
        doc = """Distance from the barn where the density interpolation
        ends."""
    )
    float analytic:density:farValue = 0 (
        doc = "Density multiplier at the end of interpolation."
    )
    float analytic:density:nearDistance = 0 (
        doc = """Distance from the barn where the density
        interpolation starts."""
    )
    float analytic:density:nearValue = 0 (
        doc = "Density multiplier where the density interpolation starts."
    )
    bool analytic:directional = 0 (
        doc = """When this is on, the texture projects along a direction
        using the orthographic projection. When it is off, the texture
        projects using a focal point specified by the analytic:apex."""
    )
    float analytic:shearX = 0 (
        doc = "Shear the projection along the X axis."
    )
    float analytic:shearY = 0 (
        doc = "Shear the projection along the Y axis."
    )
    bool analytic:useLightDirection = 0 (
        doc = """When this is on, If this is on, the projection direction
        is determined by the position of the center of the light source.
        Otherwise, it only follows the orientation of the barn. WARNING:
        This does not work with dome and mesh lights."""
    )
    token barnMode = "physical" (
        allowedTokens = ["physical", "analytic"]
        doc = """Chooses a physical or analytic evaluation model for
        the barn."""
    )
    uniform bool collection:filterLink:includeRoot = 1
    float edge:bottom = 0 (
        doc = "Additional adjustment to the top region."
    )
    float edge:left = 0 (
        doc = "Additional adjustment to the left region."
    )
    float edge:right = 0 (
        doc = "Additional adjustment to the left region."
    )
    float edge:top = 0 (
        doc = "Additional adjustment to the top region."
    )
    float edgeThickness = 0 (
        doc = """Thickness of the edge region.  Larger values will
        soften the edge shape."""
    )
    float height = 1 (
        doc = "Height of the inner region of the barn (Y axis)."
    )
    token preBarnEffect = "noEffect" (
        allowedTokens = ["noEffect", "cone", "noLight"]
        doc = """The effect on light before it reaches the barn
        geometry."""
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = '''Purpose is a concept we have found useful in our pipeline for 
        classifying geometry into categories that can each be independently
        included or excluded from traversals of prims on a stage, such as
        rendering or bounding-box computation traversals.  The fallback
        purpose, default indicates that a prim has "no special purpose"
        and should generally be included in all traversals.  Subtrees rooted
        at a prim with purpose render should generally only be included
        when performing a "final quality" render.  Subtrees rooted at a prim
        with purpose proxy should generally only be included when 
        performing a lightweight proxy render (such as openGL).  Finally,
        subtrees rooted at a prim with purpose guide should generally
        only be included when an interactive application has been explicitly
        asked to "show guides". 
        
        In the previous paragraph, when we say "subtrees rooted at a prim",
        we mean the most ancestral or tallest subtree that has an authored,
        non-default opinion.  If the purpose of </RootPrim> is set to 
        "render", then the effective purpose of </RootPrim/ChildPrim> will
        be "render" even if that prim has a different authored value for
        purpose.  <b>See ComputePurpose() for details of how purpose 
        inherits down namespace</b>.
        
        As demonstrated in UsdGeomBBoxCache, a traverser should be ready to 
        accept combinations of included purposes as an input.
        
        Purpose render can be useful in creating "light blocker"
        geometry for raytracing interior scenes.  Purposes render and
        proxy can be used together to partition a complicated model
        into a lightweight proxy representation for interactive use, and a
        fully realized, potentially quite heavy, representation for rendering.
        One can use UsdVariantSets to create proxy representations, but doing
        so requires that we recompose parts of the UsdStage in order to change
        to a different runtime level of detail, and that does not interact
        well with the needs of multithreaded rendering. Purpose provides us with
        a better tool for dynamic, interactive complexity management.'''
    )
    float radius = 0.5 (
        doc = "Radius of the corners of the inner barn square."
    )
    float refine:bottom = 0 (
        doc = "Additional adjustment to the top region."
    )
    float refine:left = 0 (
        doc = "Additional adjustment to the left region."
    )
    float refine:right = 0 (
        doc = "Additional adjustment to the left region."
    )
    float refine:top = 0 (
        doc = "Additional adjustment to the top region."
    )
    float scale:height = 1 (
        doc = "Scale the height of the inner barn shape."
    )
    float scale:width = 1 (
        doc = "Scale the width of the inner barn shape."
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    float width = 1 (
        doc = "Width of the inner region of the barn (X axis)."
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class PxrCookieLightFilter "PxrCookieLightFilter" (
    apiSchemas = ["CollectionAPI:filterLink"]
    doc = "A textured surface that filters light."
)
{
    float analytic:apex = 0 (
        doc = "Shear the projection along the Y axis."
    )
    float analytic:blur:amount = 0 (
        doc = """Specify the blur of projected texture from 0-1. This
        gets multiplied by the blurNear/blurFar interpolation. This
        blurs between the projected color and the fill color when the
        texture is not repeating."""
    )
    float analytic:blur:exponent = 0 (
        doc = "Power exponent of the blur interpolation."
    )
    float analytic:blur:farDistance = 0 (
        doc = "Distance from the cookie where the blur interpolation ends."
    )
    float analytic:blur:farValue = 0 (
        doc = "Blur multiplier at the end of interpolation."
    )
    float analytic:blur:midpoint = 0 (
        doc = "Distance between near and far where midValue is located."
    )
    float analytic:blur:midValue = 0 (
        doc = "Blur multiplier in the middle of interpolation."
    )
    float analytic:blur:nearDistance = 0 (
        doc = """Distance from the cookie where the blur interpolation
        starts."""
    )
    float analytic:blur:nearValue = 0 (
        doc = "Blur multiplier where the blur interpolation starts."
    )
    float analytic:blur:sMult = 0 (
        doc = "Blur multiplier in the S direction."
    )
    float analytic:blur:tMult = 0 (
        doc = "Blur multiplier in the T direction."
    )
    float analytic:density:exponent = 0 (
        doc = "Power exponent of the density interpolation."
    )
    float analytic:density:farDistance = 0 (
        doc = """Distance from the cookie where the density interpolation
        ends."""
    )
    float analytic:density:farValue = 0 (
        doc = "Density multiplier at the end of interpolation."
    )
    float analytic:density:midpoint = 0 (
        doc = "Distance between near and far where midValue is located."
    )
    float analytic:density:midValue = 0 (
        doc = "Density multiplier in the middle of interpolation."
    )
    float analytic:density:nearDistance = 0 (
        doc = """Distance from the cookie where the density
        interpolation starts."""
    )
    float analytic:density:nearValue = 0 (
        doc = "Density multiplier where the density interpolation starts."
    )
    bool analytic:directional = 0 (
        doc = """When this is on, the texture projects along a direction
        using the orthographic projection. When it is off, the texture
        projects using a focal point specified by the analytic:apex."""
    )
    float analytic:shearX = 0 (
        doc = "Shear the projection along the X axis."
    )
    float analytic:shearY = 0 (
        doc = "Shear the projection along the Y axis."
    )
    bool analytic:useLightDirection = 0 (
        doc = """When this is on, If this is on, the projection direction
        is determined by the position of the center of the light source.
        Otherwise, it only follows the orientation of the filter. WARNING:
        This does not work with dome and mesh lights."""
    )
    uniform bool collection:filterLink:includeRoot = 1
    float color:contrast = 1 (
        doc = """Contrast control (less than 1 = contrast reduction,
        larger than 1 = contrast increase)."""
    )
    float color:midpoint = 0.18 (
        doc = "Midpoint for the contrast control."
    )
    float color:saturation = 1 (
        doc = """Saturation of the result (0=greyscale, 1=normal,
        >1=boosted colors)."""
    )
    color3f color:tint (
        doc = """Tint of the resulting color after saturation, contrast
        and clamp."""
    )
    float color:whitepoint = 1 (
        doc = "White point for the contrast control if (contrast > 1.0)."
    )
    token cookieMode = "physical" (
        allowedTokens = ["physical", "analytic"]
        doc = """Chooses a physical or analytic evaluation model for
        the cookie:
        - physical: The cookie behaves like a stained glass window
          through which light falls. The falloff and blur are determined
          by the size of the light, the distance to the light and distance
          from the cookie.
        - analytic: The cookie has a fixed projection and manual blur
          and falloff controls.
        """
    )
    float height = 1 (
        doc = "Height of the rect the light is shining through."
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = '''Purpose is a concept we have found useful in our pipeline for 
        classifying geometry into categories that can each be independently
        included or excluded from traversals of prims on a stage, such as
        rendering or bounding-box computation traversals.  The fallback
        purpose, default indicates that a prim has "no special purpose"
        and should generally be included in all traversals.  Subtrees rooted
        at a prim with purpose render should generally only be included
        when performing a "final quality" render.  Subtrees rooted at a prim
        with purpose proxy should generally only be included when 
        performing a lightweight proxy render (such as openGL).  Finally,
        subtrees rooted at a prim with purpose guide should generally
        only be included when an interactive application has been explicitly
        asked to "show guides". 
        
        In the previous paragraph, when we say "subtrees rooted at a prim",
        we mean the most ancestral or tallest subtree that has an authored,
        non-default opinion.  If the purpose of </RootPrim> is set to 
        "render", then the effective purpose of </RootPrim/ChildPrim> will
        be "render" even if that prim has a different authored value for
        purpose.  <b>See ComputePurpose() for details of how purpose 
        inherits down namespace</b>.
        
        As demonstrated in UsdGeomBBoxCache, a traverser should be ready to 
        accept combinations of included purposes as an input.
        
        Purpose render can be useful in creating "light blocker"
        geometry for raytracing interior scenes.  Purposes render and
        proxy can be used together to partition a complicated model
        into a lightweight proxy representation for interactive use, and a
        fully realized, potentially quite heavy, representation for rendering.
        One can use UsdVariantSets to create proxy representations, but doing
        so requires that we recompose parts of the UsdStage in order to change
        to a different runtime level of detail, and that does not interact
        well with the needs of multithreaded rendering. Purpose provides us with
        a better tool for dynamic, interactive complexity management.'''
    )
    color3f texture:fillColor (
        doc = """If the texture is not repeating, this specifies the
        color for the region outside of and behind the projected rectangle."""
    )
    bool texture:invertU = 0 (
        doc = """Flips the texture from left to right. By default, the
        orientation of the texture as seen from the light source matches
        the orientation as it is viewed in an image viewer."""
    )
    bool texture:invertV = 0 (
        doc = """Flips the texture from top to bottom. By default, the
        orientation of the texture as seen from the light source matches
        the orientation as it is viewed in an image viewer."""
    )
    asset texture:map (
        doc = "A color texture to use on the cookie.  May use alpha."
    )
    float texture:offsetU = 0 (
        doc = "Offsets the texture in the U direction."
    )
    float texture:offsetV = 0 (
        doc = "Offsets the texture in the V direction."
    )
    float texture:scaleU = 1 (
        doc = "Scales the U dimension."
    )
    float texture:scaleV = 1 (
        doc = "Scales the V dimension."
    )
    token texture:wrapMode = "off" (
        allowedTokens = ["off", "repeat", "clamp"]
        doc = """Specifies what value to use outside the texture's domain:
        - off: no repeat
        - repeat: repeats in X and Y
        - clamp: uses the value from the nearest edge
        """
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    float width = 1 (
        doc = "Width of the rect the light is shining through."
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class PxrRampLightFilter "PxrRampLightFilter" (
    apiSchemas = ["CollectionAPI:filterLink"]
    doc = "A ramp to modulate how a light falls off with distance."
)
{
    uniform bool collection:filterLink:includeRoot = 1
    float falloffRamp:beginDistance = 0
    float falloffRamp:endDistance = 10
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = '''Purpose is a concept we have found useful in our pipeline for 
        classifying geometry into categories that can each be independently
        included or excluded from traversals of prims on a stage, such as
        rendering or bounding-box computation traversals.  The fallback
        purpose, default indicates that a prim has "no special purpose"
        and should generally be included in all traversals.  Subtrees rooted
        at a prim with purpose render should generally only be included
        when performing a "final quality" render.  Subtrees rooted at a prim
        with purpose proxy should generally only be included when 
        performing a lightweight proxy render (such as openGL).  Finally,
        subtrees rooted at a prim with purpose guide should generally
        only be included when an interactive application has been explicitly
        asked to "show guides". 
        
        In the previous paragraph, when we say "subtrees rooted at a prim",
        we mean the most ancestral or tallest subtree that has an authored,
        non-default opinion.  If the purpose of </RootPrim> is set to 
        "render", then the effective purpose of </RootPrim/ChildPrim> will
        be "render" even if that prim has a different authored value for
        purpose.  <b>See ComputePurpose() for details of how purpose 
        inherits down namespace</b>.
        
        As demonstrated in UsdGeomBBoxCache, a traverser should be ready to 
        accept combinations of included purposes as an input.
        
        Purpose render can be useful in creating "light blocker"
        geometry for raytracing interior scenes.  Purposes render and
        proxy can be used together to partition a complicated model
        into a lightweight proxy representation for interactive use, and a
        fully realized, potentially quite heavy, representation for rendering.
        One can use UsdVariantSets to create proxy representations, but doing
        so requires that we recompose parts of the UsdStage in order to change
        to a different runtime level of detail, and that does not interact
        well with the needs of multithreaded rendering. Purpose provides us with
        a better tool for dynamic, interactive complexity management.'''
    )
    token rampMode = "distanceToLight" (
        allowedTokens = ["distanceToLight", "linear", "spherical", "radial"]
        doc = "Specifies the direction in which the ramp is applied"
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class PxrRodLightFilter "PxrRodLightFilter" (
    apiSchemas = ["CollectionAPI:filterLink"]
    doc = "Simulates a rod or capsule-shaped region to modulate light."
)
{
    uniform bool collection:filterLink:includeRoot = 1
    float color:saturation = 1 (
        doc = """Saturation of the result (0=greyscale, 1=normal,
        >1=boosted colors)."""
    )
    float depth = 1 (
        doc = "Depth of the inner region of the rod (Z axis)."
    )
    float edge:back = 0 (
        doc = "Additional adjustment to the back region."
    )
    float edge:bottom = 0 (
        doc = "Additional adjustment to the top region."
    )
    float edge:front = 0 (
        doc = "Additional adjustment to the front region."
    )
    float edge:left = 0 (
        doc = "Additional adjustment to the left region."
    )
    float edge:right = 0 (
        doc = "Additional adjustment to the left region."
    )
    float edge:top = 0 (
        doc = "Additional adjustment to the top region."
    )
    float edgeThickness = 0 (
        doc = """Thickness of the edge region.  Larger values will
        soften the edge shape."""
    )
    float height = 1 (
        doc = "Height of the inner region of the rod (Y axis)."
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = '''Purpose is a concept we have found useful in our pipeline for 
        classifying geometry into categories that can each be independently
        included or excluded from traversals of prims on a stage, such as
        rendering or bounding-box computation traversals.  The fallback
        purpose, default indicates that a prim has "no special purpose"
        and should generally be included in all traversals.  Subtrees rooted
        at a prim with purpose render should generally only be included
        when performing a "final quality" render.  Subtrees rooted at a prim
        with purpose proxy should generally only be included when 
        performing a lightweight proxy render (such as openGL).  Finally,
        subtrees rooted at a prim with purpose guide should generally
        only be included when an interactive application has been explicitly
        asked to "show guides". 
        
        In the previous paragraph, when we say "subtrees rooted at a prim",
        we mean the most ancestral or tallest subtree that has an authored,
        non-default opinion.  If the purpose of </RootPrim> is set to 
        "render", then the effective purpose of </RootPrim/ChildPrim> will
        be "render" even if that prim has a different authored value for
        purpose.  <b>See ComputePurpose() for details of how purpose 
        inherits down namespace</b>.
        
        As demonstrated in UsdGeomBBoxCache, a traverser should be ready to 
        accept combinations of included purposes as an input.
        
        Purpose render can be useful in creating "light blocker"
        geometry for raytracing interior scenes.  Purposes render and
        proxy can be used together to partition a complicated model
        into a lightweight proxy representation for interactive use, and a
        fully realized, potentially quite heavy, representation for rendering.
        One can use UsdVariantSets to create proxy representations, but doing
        so requires that we recompose parts of the UsdStage in order to change
        to a different runtime level of detail, and that does not interact
        well with the needs of multithreaded rendering. Purpose provides us with
        a better tool for dynamic, interactive complexity management.'''
    )
    float radius = 0.5 (
        doc = "Radius of the corners of the inner rod box."
    )
    float refine:back = 0 (
        doc = "Additional adjustment to the back region."
    )
    float refine:bottom = 0 (
        doc = "Additional adjustment to the top region."
    )
    float refine:front = 0 (
        doc = "Additional adjustment to the front region."
    )
    float refine:left = 0 (
        doc = "Additional adjustment to the left region."
    )
    float refine:right = 0 (
        doc = "Additional adjustment to the left region."
    )
    float refine:top = 0 (
        doc = "Additional adjustment to the top region."
    )
    float scale:depth = 1 (
        doc = "Scale the depth of the inner rod shape."
    )
    float scale:height = 1 (
        doc = "Scale the height of the inner rod shape."
    )
    float scale:width = 1 (
        doc = "Scale the width of the inner rod shape."
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    float width = 1 (
        doc = "Width of the inner region of the rod (X axis)."
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

