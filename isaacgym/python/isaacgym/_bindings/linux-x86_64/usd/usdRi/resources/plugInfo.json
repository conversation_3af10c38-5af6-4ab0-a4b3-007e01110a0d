# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdRiLightAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiLightAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdRiLightFilterAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiLightFilterAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdRiLightPortalAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiLightPortalAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdRiMaterialAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiMaterialAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdRiPxrAovLight": {
                        "alias": {
                            "UsdSchemaBase": "PxrAovLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdRiPxrBarnLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "PxrBarnLightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLightFilter"
                        ]
                    }, 
                    "UsdRiPxrCookieLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "PxrCookieLightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLightFilter"
                        ]
                    }, 
                    "UsdRiPxrEnvDayLight": {
                        "alias": {
                            "UsdSchemaBase": "PxrEnvDayLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdRiPxrIntMultLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "PxrIntMultLightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLightFilter"
                        ]
                    }, 
                    "UsdRiPxrRampLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "PxrRampLightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLightFilter"
                        ]
                    }, 
                    "UsdRiPxrRodLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "PxrRodLightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLightFilter"
                        ]
                    }, 
                    "UsdRiRisBxdf": {
                        "alias": {
                            "UsdSchemaBase": "RisBxdf"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdRiRisObject"
                        ]
                    }, 
                    "UsdRiRisIntegrator": {
                        "alias": {
                            "UsdSchemaBase": "RisIntegrator"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdRiRisObject": {
                        "alias": {
                            "UsdSchemaBase": "RisObject"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdShadeShader"
                        ]
                    }, 
                    "UsdRiRisOslPattern": {
                        "alias": {
                            "UsdSchemaBase": "RisOslPattern"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdRiRisPattern"
                        ]
                    }, 
                    "UsdRiRisPattern": {
                        "alias": {
                            "UsdSchemaBase": "RisPattern"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdRiRisObject"
                        ]
                    }, 
                    "UsdRiRslShader": {
                        "alias": {
                            "UsdSchemaBase": "RslShader"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdShadeShader"
                        ]
                    }, 
                    "UsdRiSplineAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiSplineAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdRiStatementsAPI": {
                        "alias": {
                            "UsdSchemaBase": "StatementsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdRiTextureAPI": {
                        "alias": {
                            "UsdSchemaBase": "RiTextureAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }
                }
            }, 
            "LibraryPath": "../../libusdRi.so", 
            "Name": "usdRi", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
