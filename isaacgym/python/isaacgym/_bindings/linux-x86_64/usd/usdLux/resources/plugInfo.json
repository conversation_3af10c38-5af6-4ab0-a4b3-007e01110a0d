# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdLuxCylinderLight": {
                        "alias": {
                            "UsdSchemaBase": "CylinderLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdLuxDiskLight": {
                        "alias": {
                            "UsdSchemaBase": "DiskLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdLuxDistantLight": {
                        "alias": {
                            "UsdSchemaBase": "DistantLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdLuxDomeLight": {
                        "alias": {
                            "UsdSchemaBase": "DomeLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdLuxGeometryLight": {
                        "alias": {
                            "UsdSchemaBase": "GeometryLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdLuxLight": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ]
                    }, 
                    "UsdLuxLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "LightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ]
                    }, 
                    "UsdLuxLightPortal": {
                        "alias": {
                            "UsdSchemaBase": "LightPortal"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ]
                    }, 
                    "UsdLuxListAPI": {
                        "alias": {
                            "UsdSchemaBase": "ListAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdLuxRectLight": {
                        "alias": {
                            "UsdSchemaBase": "RectLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }, 
                    "UsdLuxShadowAPI": {
                        "alias": {
                            "UsdSchemaBase": "ShadowAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdLuxShapingAPI": {
                        "alias": {
                            "UsdSchemaBase": "ShapingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdLuxSphereLight": {
                        "alias": {
                            "UsdSchemaBase": "SphereLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLight"
                        ]
                    }
                }
            }, 
            "LibraryPath": "../../libusdLux.so", 
            "Name": "usdLux", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
