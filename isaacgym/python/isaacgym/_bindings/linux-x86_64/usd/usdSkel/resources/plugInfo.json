# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "weight": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": 0, 
                        "displayGroup": "BlendShape", 
                        "documentation": "The weight value at which an inbeteen shape is applied.", 
                        "type": "float"
                    }
                }, 
                "Types": {
                    "UsdSkelAnimation": {
                        "alias": {
                            "UsdSchemaBase": "SkelAnimation"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdSkelBindingAPI": {
                        "alias": {
                            "UsdSchemaBase": "SkelBindingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdSkelBlendShape": {
                        "alias": {
                            "UsdSchemaBase": "BlendShape"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdSkelPackedJointAnimation": {
                        "alias": {
                            "UsdSchemaBase": "PackedJointAnimation"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdSkelAnimation"
                        ]
                    }, 
                    "UsdSkelRoot": {
                        "alias": {
                            "UsdSchemaBase": "SkelRoot"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "implementsComputeExtent": true
                    }, 
                    "UsdSkelSkeleton": {
                        "alias": {
                            "UsdSchemaBase": "Skeleton"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "implementsComputeExtent": true
                    }
                }
            }, 
            "LibraryPath": "../../libusdSkel.so", 
            "Name": "usdSkel", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
