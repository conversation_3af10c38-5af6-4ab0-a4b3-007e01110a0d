#usda 1.0

(
    subLayers = [
        @usdGeom/schema.usda@
    ]
)
 
over "GLOBAL" (
    customData = {
        string libraryName = "usdVol"
        string libraryPath = "pxr/usd/usdVol"
	dictionary libraryTokens = {
            dictionary field = {
                string doc = """This is the namespace prefix used to 
                specify the fields that make up a volume primitive."""
            }
	}
    }
)
{
}

class Volume "Volume" (
    inherits = </Gprim>
    doc = """A renderable volume primitive. A volume is made up of any number
             of FieldBase primitives bound together in this volume. Each
             FieldBase primitive is specified as a relationship with a
             namespace prefix of "field".

             The relationship name is used by the renderer to associate
             individual fields with the named input parameters on the volume
             shader. Using this indirect approach to connecting fields to
             shader parameters (rather than using the field prim's name)
             allows a single field to be reused for different shader inputs, or
             to be used as different shader parameters when rendering different
             Volumes. This means that the name of the field prim is not
             relevant to its contribution to the volume prims which refer to
             it. Nor does the field prim's location in the scene graph have
             any relevance, and Volumes may refer to fields anywhere in the
             scene graph.  **However**, unless Field prims need to be shared
             by multiple Volumes, a Volume's Field prims should be located
             under the Volume in namespace, for enhanced organization."""
)
{
}

class "FieldBase" (
    inherits = </Boundable>
    doc = """Base class for field primitives."""
)
{
}

class "FieldAsset" (
    inherits = </FieldBase>
    doc = """Base class for field primitives defined by an external file."""
)
{
    asset filePath (
	doc = """An asset path attribute that points to a file on disk.
                 For each supported file format, a separate FieldAsset
                 subclass is required. Any further information required to
                 extract the field from the file (such as a name or index to
                 choose a single field from a file that can store multiple
                 fields) will be defined on the FieldAsset subclass.
                 
                 This attribute's value can be animated over time, as most
                 volume asset formats represent just a single timeSample of
                 a volume.  However, it does not, at this time, support
                 any pattern substitutions like "$F". """
    )
}

class Field3DAsset "Field3DAsset" (
    inherits = </FieldAsset>
    doc = """Field3D field primitive. The FieldAsset filePath attribute must
             specify a file in the Field3D format on disk."""
)
{
    token fieldName (
	doc = """Name of an individual field within the file specified by
                 the filePath attribute."""
    )

    int fieldIndex (
	doc = """A Field3D file can contain multiple fields with the same
                 name. This attribute is an index used to disambiguate
                 between these multiple fields with the same name."""
    )
}

class OpenVDBAsset "OpenVDBAsset" (
    inherits = </FieldAsset>
    doc = """OpenVDB field primitive. The FieldAsset filePath attribute must
             specify a file in the OpenVDB format on disk."""
)
{
    token fieldName (
	doc = """Name of an individual grid within the file specified by
                 the filePath attribute."""
    )
}

