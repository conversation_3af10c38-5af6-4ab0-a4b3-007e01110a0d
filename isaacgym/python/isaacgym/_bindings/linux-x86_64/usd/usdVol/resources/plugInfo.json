# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdVolField3DAsset": {
                        "alias": {
                            "UsdSchemaBase": "Field3DAsset"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdVolFieldAsset"
                        ]
                    }, 
                    "UsdVolFieldAsset": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdVolFieldBase"
                        ]
                    }, 
                    "UsdVolFieldBase": {
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ]
                    }, 
                    "UsdVolOpenVDBAsset": {
                        "alias": {
                            "UsdSchemaBase": "OpenVDBAsset"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdVolFieldAsset"
                        ]
                    }, 
                    "UsdVolVolume": {
                        "alias": {
                            "UsdSchemaBase": "Volume"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ]
                    }
                }
            }, 
            "LibraryPath": "../../libusdVol.so", 
            "Name": "usdVol", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
