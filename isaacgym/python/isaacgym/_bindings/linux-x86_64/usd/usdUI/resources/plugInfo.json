# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdUIBackdrop": {
                        "alias": {
                            "UsdSchemaBase": "Backdrop"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ]
                    }, 
                    "UsdUINodeGraphNodeAPI": {
                        "alias": {
                            "UsdSchemaBase": "NodeGraphNodeAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }, 
                    "UsdUISceneGraphPrimAPI": {
                        "alias": {
                            "UsdSchemaBase": "SceneGraphPrimAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ]
                    }
                }
            }, 
            "LibraryPath": "../../libusdUI.so", 
            "Name": "usdUI", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
