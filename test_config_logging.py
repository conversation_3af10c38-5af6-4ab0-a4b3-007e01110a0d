#!/usr/bin/env python3
"""
Test script to verify config logging functionality
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# Add the project root to Python path
sys.path.append('/media/lmm/myxt/Gao/reinforce-learning/HIMLoco')

from legged_gym.envs import *
from legged_gym.utils import get_args, task_registry

def test_config_logging():
    """Test the config logging functionality"""
    print("Testing config logging functionality...")
    
    # Create temporary directory for testing
    temp_dir = tempfile.mkdtemp(prefix='test_config_logging_')
    print(f"Using temporary directory: {temp_dir}")
    
    try:
        # Get default args
        args = get_args()
        args.task = "ysc4go"
        args.headless = True
        args.num_envs = 4  # Very small number for testing
        args.max_iterations = 1  # Very short training for testing
        args.sim_device = "cpu"  # Use CPU to avoid CUDA memory issues
        args.rl_device = "cpu"
        
        # Create environment
        print("Creating environment...")
        env, env_cfg = task_registry.make_env(name=args.task, args=args)
        
        # Create runner with custom log directory
        print("Creating runner...")
        ppo_runner, train_cfg = task_registry.make_alg_runner(
            env=env, 
            name=args.task, 
            args=args, 
            log_root=temp_dir
        )
        
        # Start training (this should trigger config saving)
        print("Starting training (this will save configs)...")
        ppo_runner.learn(num_learning_iterations=1, init_at_random_ep_len=True)
        
        # Check if config files were created
        log_dir = ppo_runner.log_dir
        if log_dir:
            config_dir = os.path.join(log_dir, 'configs')
            json_path = os.path.join(config_dir, 'config.json')
            yaml_path = os.path.join(config_dir, 'config.yaml')
            
            print(f"\nChecking config files in: {config_dir}")
            
            if os.path.exists(json_path):
                print("✓ config.json created successfully")
                # Show file size
                size = os.path.getsize(json_path)
                print(f"  File size: {size} bytes")
            else:
                print("✗ config.json not found")
            
            if os.path.exists(yaml_path):
                print("✓ config.yaml created successfully")
                # Show file size
                size = os.path.getsize(yaml_path)
                print(f"  File size: {size} bytes")
            else:
                print("✗ config.yaml not found")
            
            # Check TensorBoard logs
            tb_files = [f for f in os.listdir(log_dir) if f.startswith('events.out.tfevents')]
            if tb_files:
                print("✓ TensorBoard event files created")
                print(f"  Found {len(tb_files)} event file(s)")
            else:
                print("✗ No TensorBoard event files found")
            
            # Show directory structure
            print(f"\nLog directory structure:")
            for root, dirs, files in os.walk(log_dir):
                level = root.replace(log_dir, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files:
                    print(f"{subindent}{file}")
        
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up temporary directory
        try:
            shutil.rmtree(temp_dir)
            print(f"Cleaned up temporary directory: {temp_dir}")
        except Exception as e:
            print(f"Warning: Failed to clean up temporary directory: {e}")

if __name__ == "__main__":
    test_config_logging()
