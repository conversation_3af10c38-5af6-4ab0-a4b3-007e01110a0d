import torch
import io
import sys
import os
from zipfile import ZipFile
import argparse

def print_tensor_info(name, tensor):
    """打印张量的基本信息"""
    print(f"  {name}:")
    print(f"    Shape: {tensor.shape}")
    print(f"    Dtype: {tensor.dtype}")
    print(f"    Device: {tensor.device}")
    print(f"    Min/Max: {tensor.min().item():.6f}/{tensor.max().item():.6f}")
    if tensor.numel() < 10:
        print(f"    Values: {tensor}")
    else:
        print(f"    First few values: {tensor.flatten()[:5]}")

def print_module_info(module, prefix=""):
    """递归打印模块的结构和参数"""
    for name, child in module.named_children():
        print(f"{prefix}{name} ({type(child).__name__})")
        print_module_info(child, prefix + "  ")
    
    for name, param in module.named_parameters(recurse=False):
        print(f"{prefix}Parameter: {name} - Shape: {param.shape}")

def explore_pt_file(file_path):
    """探索并打印PT文件的内容"""
    print(f"Exploring PT file: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist")
        return
    
    # 尝试作为普通模型加载
    try:
        model = torch.load(file_path, map_location=torch.device('cpu'))
        print("\n=== Loaded as regular PyTorch model ===")
        
        if isinstance(model, dict):
            print("Model is a dictionary with keys:")
            for key in model.keys():
                print(f"  {key}")
                if isinstance(model[key], torch.Tensor):
                    print_tensor_info(key, model[key])
                elif hasattr(model[key], 'named_parameters'):
                    print_module_info(model[key])
        elif hasattr(model, 'named_parameters'):
            print("Model structure:")
            print_module_info(model)
        else:
            print(f"Model type: {type(model)}")
            print(model)
        
        return
    except Exception as e:
        print(f"Not a regular PyTorch model: {e}")
    
    # 尝试作为TorchScript模型加载
    try:
        model = torch.jit.load(file_path)
        print("\n=== Loaded as TorchScript model ===")
        print(f"Model type: {type(model)}")
        print("Model code:")
        print(model.code)
        print("\nModel parameters:")
        for name, param in model.named_parameters():
            print(f"  {name} - Shape: {param.shape}")
        return
    except Exception as e:
        print(f"Not a TorchScript model: {e}")
    
    # 尝试作为Package加载
    try:
        # 检查是否为ZIP文件（torch.package使用ZIP格式）
        if not zipfile.is_zipfile(file_path):
            print("Not a ZIP file, cannot be a torch.package")
            return
            
        print("\n=== Exploring as torch.package ===")
        with ZipFile(file_path) as package_zip:
            print("Package contents:")
            for file_info in package_zip.infolist():
                print(f"  {file_info.filename} - {file_info.file_size} bytes")
            
            # 尝试读取一些特定文件
            if '.data/version' in package_zip.namelist():
                version = package_zip.read('.data/version').decode('utf-8')
                print(f"Package version: {version}")
                
        # 尝试使用PackageImporter加载
        try:
            from torch.package import PackageImporter
            importer = PackageImporter(file_path)
            print("\nPackage structure:")
            print(importer.file_structure())
        except Exception as e:
            print(f"Could not use PackageImporter: {e}")
            
    except Exception as e:
        print(f"Error exploring as package: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Print information about a PyTorch .pt file")
    parser.add_argument("file_path", help="Path to the .pt file")
    args = parser.parse_args()
    
    explore_pt_file(args.file_path)