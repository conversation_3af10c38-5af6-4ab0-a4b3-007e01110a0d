## Learning-based Locomotion Control from OpenRobotLab
This repository contains learning-based locomotion control research from OpenRobotLab, currently including [Hybrid Internal Model](/projects/himloco/README.md) & [H-Infinity Locomotion Control](/projects/h_infinity/README.md).
## 🔥 News
- [2024-04] Code of HIMLoco is released.
- [2024-04] We release the [paper](https://arxiv.org/abs/2404.14405) of H-Infinity Locomotion Control. Please check the :point_right: [webpage](https://junfeng-long.github.io/HINF/) :point_left: and view our demos! :sparkler:
- [2024-01] HIMLoco is accepted by ICLR 2024.
- [2023-12] We release the [paper](https://arxiv.org/abs/2312.11460) of HIMLoco. Please check the :point_right: [webpage](https://junfeng-long.github.io/HIMLoco/) :point_left: and view our demos! :sparkler:

## 📝 TODO List
- \[x\] Release the training code of HIMLoco, please see `rsl_rl/rsl_rl/algorithms/him_ppo.py`.
- \[ \] Release deployment guidance of HIMLoco.
- \[ \] Release the training code of H-Infinity Locomotion Control.
- \[ \] Release deployment guidance of H-Infinity Locomotion Control.

## 📚 Getting Started

### Installation

We test our codes under the following environment:

- Ubuntu 20.04
- NVIDIA Driver: 525.147.05
- CUDA 12.0
- Python 3.7.16
- PyTorch 1.10.0+cu113
- Isaac Gym: Preview 4

1. Create an environment and install PyTorch:

  - `conda create -n himloco python=3.7.16`
  - `conda activate himloco`
  - `pip3 install torch==1.10.0+cu113 torchvision==0.11.1+cu113 torchaudio==0.10.0+cu113 -f https://download.pytorch.org/whl/cu113/torch_stable.html`

2. Install Isaac Gym:
  - Download and install Isaac Gym Preview 4 from https://developer.nvidia.com/isaac-gym
  - `cd isaacgym/python && pip install -e .`

3. Clone this repository.

  - `git clone https://github.com/OpenRobotLab/HIMLoco.git`
  - `cd HIMLoco`


4. Install HIMLoco.
  - `cd rsl_rl && pip install -e .`
  - `cd ../legged_gym && pip install -e .`

**Note:** Please use legged_gym and rsl_rl provided in this repo, we have modefications on these repos.

### Tutorial

1. Train a policy:

  - `cd legged_gym/legged_gym/scripts`
  - `python train.py`

2. Play and export the latest policy:
  - `cd legged_gym/legged_gym/scripts`
  - `python play.py`



## 🔗 Citation

If you find our work helpful, please cite:

```bibtex
@inproceedings{long2023him,
  title={Hybrid Internal Model: Learning Agile Legged Locomotion with Simulated Robot Response},
  author={Long, Junfeng and Wang, ZiRui and Li, Quanyi and Cao, Liu and Gao, Jiawei and Pang, Jiangmiao},
  booktitle={The Twelfth International Conference on Learning Representations},
  year={2024}
}

@misc{long2024hinf,
  title={Learning H-Infinity Locomotion Control}, 
  author={Junfeng Long and Wenye Yu and Quanyi Li and Zirui Wang and Dahua Lin and Jiangmiao Pang},
  year={2024},
  eprint={2404.14405},
  archivePrefix={arXiv},
}
```

## 📄 License
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="Creative Commons License" style="border-width:0" src="https://i.creativecommons.org/l/by-nc-sa/4.0/80x15.png" /></a>
<br />
This work is under the <a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International License</a>.

## 👏 Acknowledgements
- [legged_gym](https://github.com/leggedrobotics/legged_gym): Our codebase is built upon legged_gym.







  python train.py --task=ysc4go
  python play_joystick.py --task=ysc4go
  python play_joystick.py --task=ysc4go --load_run=May27_11-30-09_ --checkpoint=3200

-m debugpy --listen 8934 --wait-for-client

(actor): Sequential(
  (0): Linear(in_features=64, out_features=512, bias=True)
  (1): ELU(alpha=1.0)
  (2): Linear(in_features=512, out_features=256, bias=True)
  (3): ELU(alpha=1.0)
  (4): Linear(in_features=256, out_features=128, bias=True)
  (5): ELU(alpha=1.0)
  (6): Linear(in_features=128, out_features=12, bias=True)
)

(encoder): Sequential(
  (0): Linear(in_features=270, out_features=128, bias=True)
  (1): ELU(alpha=1.0)
  (2): Linear(in_features=128, out_features=64, bias=True)
  (3): ELU(alpha=1.0)
  (4): Linear(in_features=64, out_features=19, bias=True)
)

current_obs = torch.cat((   self.commands[:, :3] * self.commands_scale,
                            self.base_ang_vel  * self.obs_scales.ang_vel,
                            self.projected_gravity,
                            (self.dof_pos - self.default_dof_pos) * self.obs_scales.dof_pos,
                            self.dof_vel * self.obs_scales.dof_vel,
                            self.actions
                            ),dim=-1)

self.obs_buf = torch.cat((current_obs[:, :self.num_one_step_obs], self.obs_buf[:, :-self.num_one_step_obs]), dim=-1)


python legged_gym/legged_gym/scripts/play.py 导出.pt
python legged_gym/legged_gym/scripts/export_policy_to_onnx.py 导出.onnx


python legged_gym/legged_gym/scripts/play_joystick.py  --task=robs3go_amp  --load_run=Jul02_14-42-49_  --checkpoint=27000

小腿贴地

legged_gym/logs/rough_ysc4go/Jun03_19-39-49_ 小腿不贴地了，低速走得难看,站着姿态倾斜（右倾 后倾），腿不对称，罗圈腿 控制精度高 楼梯也还行


python legged_gym/legged_gym/scripts/train.py --task=ysc4go_amp

python legged_gym/legged_gym/scripts/play_joystick.py  --task=ysc4go_amp  --load_run=Jun06_15-12-54_  --checkpoint=40000

legged_gym/logs/rough_amp_ysc4go/Jun06_11-31-25_ 加入amp 狗散不开
Jun06_11-31-25_ 观察到reward_disc和reward_task极不平衡 疑似风格奖励比重过大 
Jun06_15-12-54_ amp_reward_coef由1改为0.01  mean_amp_policy_pred结果接近0.1，mean_amp_expert_pred结果接近0，amp_loss始终大于2，grad_pen_loss接近0，判别器完全没学会区分专家和策略

梯度惩罚强制梯度→0 → 判别器无法更新 → 输出保持初始值 → 梯度继续为0
修改梯度惩罚（试过了没用） grad_pen = lambda_ * (grad.norm(2, dim=1) - 1).pow(2).mean()  # 目标1而不是0  原梯度惩罚要求判别器梯度为0（即不学习）
破案 amp参数没有加入optimizer = =
amp_reward_coef = 0.3 风格奖励是否在一开始过于强势--->预训练后加入amp?  

Jun09_21-13-33_ amp_reward_coef = 0.3 狗走不动-->改成0.05 
Jun10_11-17-59_   （--resume --load_run=Jun09_21-13-33_  --checkpoint=5000）amp_reward_coef = 0.05 训练2500步后出现效果了 内八减轻  
Jun10_14-43-18_ terrian_level能到5.4 脚抬得不够高 上楼梯困难-->clearance_height_target = -0.18 or foot_clearance_up = -5 
Jun11_10-08-50_ 修改foot_clearance 仿真里看不出效果 还是上不了楼梯-->增加stumble foot_air_time
Jun11_16-20-35_  terrain_level 降到5以下 越障能力下降 楼梯前容易卡住
Jun12_09-20-43_ foot_clearance_up = -0.5 clearance_height_target = -0.18 stumble = -0.05  feet_air_time =  0.3 collision = -1.0 terrian_level=5.5+ 抬腿高度还是差点 转圈奇怪，身子扭-->改停止reward 挺好一版
 

Jun13_17-45-08_  randomize_start_pose = True terminate_after_contacts_on = [] upward = 0.5 狗背部着地翻不过来  40k terrian_level=5.5+ 

Jun16_14-47-40_  max_push_vel_xy = 3  amp_reward_coef = 0.1 clearance_height_target = -0.15 希望他被踢倒能滚起来 取16k 好像不灵 


换个amp专家数据



export LD_LIBRARY_PATH=/home/<USER>/miniconda3/envs/himloco/lib:$LD_LIBRARY_PATH



测试legged_gym/logs/rough_amp_robs3go/Jul02_09-04-16_/model_7800.pt





#TODO
20250714
输入归一化   done 感觉没啥用
priviledge obs 完善  done

terrian level 上不去 感觉和torque加入的motor strength扰动有关

对比学习loss done 收敛明显加快 2w步到5.8+  
python legged_gym/legged_gym/scripts/play.py  --task=robs3go_vel  --load_run=Jul14_19-48-43_  --checkpoint=24000  (在robs3go_detail文件夹下)


encoder  latent vel 分开 done  没啥用
动作symetric loss  左右对称 狗开始跳着走 失败 有空排查

低速步态很难看  把速度限制在0.5-2 速度课程细化  仿真中感觉有效果 会影响训练速度 terrian level 上升变慢

critic 对地形单独编码

        # for asymetric loss
        # self.joint_sym_mat 矩阵使关节完全对称  dof_pos dof_vel actions obs
        joint_sym_mat = torch.zeros((num_actions, num_actions), device=self.device, dtype=torch.float32, requires_grad=False)
        joint_sym_mat[:3, 3:6] = torch.eye(3, device=self.device, dtype=torch.float32)
        joint_sym_mat[0, 3] = -1.0   # 左右hip是反的
        joint_sym_mat[3:6, :3] = torch.eye(3, device=self.device, dtype=torch.float32)
        joint_sym_mat[3, 0] = -1.0
        joint_sym_mat[6:9, 9:12] = torch.eye(3, device=self.device, dtype=torch.float32)
        joint_sym_mat[6, 9] = -1.0
        joint_sym_mat[9:12, 6:9] = torch.eye(3, device=self.device, dtype=torch.float32)
        joint_sym_mat[9, 6] = -1.0
        # self.obs_sym_mat使base coordinate 左右对称
        # self.obs_sym_mat = torch.zeros((num_obs, num_obs), device=self.device, dtype=torch.float32, requires_grad=False)
        raw_obs_sym_mat = torch.eye(num_obs, device=self.device, dtype=torch.float32, requires_grad=False)
        raw_obs_sym_mat[1, 1] = -1.0  
        raw_obs_sym_mat[2, 2] = -1.0 # command
        raw_obs_sym_mat[3, 3] = -1.0 
        raw_obs_sym_mat[5, 5] = -1.0 # ang  左手系和右手系的差别
        raw_obs_sym_mat[7, 7] = -1.0 # project_gravity
        for i in range(3):
            raw_obs_sym_mat[9+i*num_actions:9+i*num_actions+num_actions, 9+i*num_actions:9+i*num_actions+num_actions]=joint_sym_mat
        
        self.obs_sym_mat = torch.zeros((6*45, 6*45), device=self.device, dtype=torch.float32, requires_grad=False)
        for i in range(6):
            self.obs_sym_mat[i*45:i*45+45, i*45:i*45+45] = raw_obs_sym_mat