#!/usr/bin/env python3
import json
import os

def convert_motion_format(input_file, output_file):
    """
    Convert motion data from a simple list of arrays to a JSON format with metadata.

    Args:
        input_file (str): Path to the input motion data file
        output_file (str): Path to save the converted motion data
    """
    print(f"Converting {input_file} to {output_file}...")

    # Read the input file
    with open(input_file, 'r') as f:
        content = f.read()

    # Parse the motion data
    # Each line is an array of motion data
    lines = content.strip().split('\n')
    frames = []

    for line in lines:
        # Remove trailing comma if present
        if line.endswith(','):
            line = line[:-1]

        # Parse the array
        try:
            # Handle the case where the line is wrapped in square brackets
            if line.startswith('[') and line.endswith(']'):
                frame_data = json.loads(line)
            else:
                # If not properly formatted, try to fix it
                frame_data = json.loads('[' + line + ']')

            frames.append(frame_data)
        except json.JSONDecodeError as e:
            print(f"Error parsing line: {line}")
            print(f"Error details: {e}")
            continue

    # Create the output JSON structure
    output_data = {
        "LoopMode": "Wrap",
        "FrameDuration": 0.021,
        "EnableCycleOffsetPosition": True,
        "EnableCycleOffsetRotation": True,
        "MotionWeight": 1.0,
        "Frames": frames
    }

    # Write the output file
    with open(output_file, 'w') as f:
        # Write the opening part manually for better formatting
        f.write("{\n")
        f.write("\"LoopMode\": \"Wrap\",\n")
        f.write("\"FrameDuration\": 0.021,\n")
        f.write("\"EnableCycleOffsetPosition\": true,\n")
        f.write("\"EnableCycleOffsetRotation\": true,\n")
        f.write("\"MotionWeight\": 1.0,\n")
        f.write("\n\"Frames\":\n[\n")

        # Write each frame
        for i, frame in enumerate(frames):
            frame_str = json.dumps(frame)
            if i < len(frames) - 1:
                f.write(frame_str + ",\n")
            else:
                f.write(frame_str + "\n")

        # Write the closing part
        f.write("]\n}")

    print(f"Conversion complete. Saved to {output_file}")

if __name__ == "__main__":
    # Define input and output paths
    input_file = "legged_gym/datasets/robs3go/record_amp_data.txt"

    # Create output directory if it doesn't exist
    output_dir = "legged_gym/datasets/robs3go"
    os.makedirs(output_dir, exist_ok=True)

    # Define output file path
    output_file = os.path.join(output_dir, "robs3go_amp_data.txt")

    # If the output directory doesn't exist, print a message
    if not os.path.exists(os.path.dirname(output_file)):
        print(f"Creating output directory: {os.path.dirname(output_file)}")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

    # Convert the file
    convert_motion_format(input_file, output_file)
