<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot.xacro                    | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="robot">
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  
  <material name="grey">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.9137254901960784 0.9137254901960784 0.8470588235294118 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.4235294117647059 0.0392156862745098 1.0"/>
  </material>
  <material name="brown">
    <color rgba="0.8705882352941177 0.8117647058823529 0.7647058823529411 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
 
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/base.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.512 0.216 0.152"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00398199 -0.00100582 -0.00545947"/>
      <mass value="7.86644305"/>
      <inertia ixx="0.03463619" ixy="3.246e-05" ixz="-0.00139993" iyy="0.10385097" iyz="-8.132e-05" izz="0.12206175"/>
    </inertial>
  </link>
  <!-- Imu is fixed to the base link -->
  <joint name="unitree_imu_joint" type="fixed">
    <origin rpy="0. 0. 0." xyz="0. 0. 0."/>
    <parent link="base"/>
    <child link="unitree_imu"/>
  </joint>
  <!-- Imu link -->
  <link name="unitree_imu">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.000001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.000001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.015 0.015 0.004"/>
      </geometry>
    </visual>
    <material name="orange">
      <color rgba="255 108 10 255"/>
    </material>
  </link>

  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.21 -0.055 0.0"/>
    <parent link="base"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="FR_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <!-- <origin rpy="${pi} 0 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="../meshes/R_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00234792 -0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="-8.15e-06" ixz="-3.85e-06" iyy="0.00116168" iyz="3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.09772 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="FR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/R_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="-0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="-0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="FR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="FR_foot_fixed" type="fixed"  dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="FR_calf"/>
    <child link="FR_foot"/>
  </joint>
  <link name="FR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="../meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <origin rpy="0 0 0" xyz="3.045e-05 0 -0.00104172"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>
  
  </link>
   <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.21 0.055 0.0"/>
    <parent link="base"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="FL_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00234792 0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="8.15e-06" ixz="-3.85e-06" iyy="0.00116168" iyz="-3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.09772 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="FL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 -0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="FL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="FL_foot_fixed" type="fixed"  dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="FL_calf"/>
    <child link="FL_foot"/>
  </joint>
  <link name="FL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="../meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>

  </link>
  
  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.21 -0.055 0.0"/>
    <parent link="base"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="RR_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="0.00234792 -0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="8.15e-06" ixz="3.85e-06" iyy="0.00116168" iyz="3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.09772 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="RR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/R_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="-0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="-0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="RR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="RR_foot_fixed" type="fixed"  dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RR_calf"/>
    <child link="RR_foot"/>
  </joint>
  <link name="RR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_robs3go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <origin rpy="0 0 0" xyz="3.045e-05 0 -0.00104172"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  
  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.21 0.055 0.0"/>
    <parent link="base"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="RL_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/R_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="0.00234792 0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="-8.15e-06" ixz="3.85e-06" iyy="0.00116168" iyz="-3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.09772 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="RL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <!-- <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 -0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="RL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/L_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="RL_foot_fixed" type="fixed"  dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RL_calf"/>
    <child link="RL_foot"/>
  </joint>
  <link name="RL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_robs3go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
       <origin rpy="0 0 0" xyz="3.045e-05 0 -0.00104172"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  
  <!-- Robot Footprint -->
  <!-- <joint name="footprint_joint" type="fixed">
        <parent link="base"/>
        <child link="base_footprint"/>
        <origin xyz="0.0 0.0 -0.2" rpy="0 0 0"/>
    </joint>

    <link name="base_footprint">
      <inertial>
      <origin xyz="0 0 0" rpy="${pi/2} 0 ${pi/2}"/>      
      <mass value="0.001"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
    </link>

    <xacro:lidar parent="base" xyz="-0.065 0. 0.1" rpy="0. 0. 0."/> -->
</robot>

